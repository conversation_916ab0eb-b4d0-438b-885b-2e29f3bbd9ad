//
//  GetTask.h
//  TrollSpeed
//
//  Created by 情情 on 2024/4/1.
//

#include <stdio.h>
#import <mach-o/dyld.h>
#import <mach/mach.h>

#include <sys/sysctl.h>
#import <string.h>
#include <string>
#include <arpa/inet.h>
#include <net/if.h>
#include <ifaddrs.h>
#import <dlfcn.h>

extern "C" kern_return_t mach_vm_region_recurse(
                                                vm_map_t                 map,
                                                mach_vm_address_t        *address,
                                                mach_vm_size_t           *size,
                                                uint32_t                 *depth,
                                                vm_region_recurse_info_t info,
                                                mach_msg_type_number_t   *infoCnt);
extern "C" kern_return_t
mach_vm_read_overwrite(
                       vm_map_t           target_task,
                       mach_vm_address_t  address,
                       mach_vm_size_t     size,
                       mach_vm_address_t  data,
                       mach_vm_size_t     *outsize);
#pragma mark 读取get_task
mach_port_t gameTask = -1;
long baseAddr = -1;

int get_Pid() {
    size_t length = 0;
    static const int name[] = {CTL_KERN, KERN_PROC, KERN_PROC_ALL, 0};
    int err = sysctl((int *)name, (sizeof(name) / sizeof(*name)) - 1, NULL, &length, NULL, 0);
    if (err == -1) err = errno;
    if (err == 0) {
        struct kinfo_proc *procBuffer = (struct kinfo_proc *)malloc(length);
        if(procBuffer == NULL) return -1;
        sysctl((int *)name, (sizeof(name) / sizeof(*name)) - 1, procBuffer, &length, NULL, 0);
        int count = (int)length / sizeof(struct kinfo_proc);
        for (int i = 0; i < count; ++i) {
            const char *procname = procBuffer[i].kp_proc.p_comm;
            NSString *进程名字=[NSString stringWithFormat:@"%s",procname];
            pid_t pid = procBuffer[i].kp_proc.p_pid;
            //自己写判断进程名 和平精英
            if([进程名字 containsString:@"ShadowTrackerExt"])
            {
                NSLog(@"[情情]:name:%@::pid:%d", 进程名字, pid);
                return pid;
            }
        }
    }
    
    return  -1;
}
long get_base_address() {
    vm_map_offset_t vmoffset = 0;
    vm_map_size_t vmsize = 0;
    uint32_t nesting_depth = 0;
    struct vm_region_submap_info_64 vbr;
    mach_msg_type_number_t vbrcount = 16;
    pid_t pid = get_Pid();
    if (pid < 0){
        return -1;
    }
    kern_return_t kret = task_for_pid(mach_task_self(), pid, &gameTask);
    NSLog(@"[情情]:pid==task=%d     kret:%d", gameTask, kret);
    if (kret == KERN_SUCCESS) {
        NSLog(@"[情情]:task=%d", gameTask);
        mach_vm_region_recurse(gameTask, &vmoffset, &vmsize, &nesting_depth, (vm_region_recurse_info_t)&vbr, &vbrcount);
        return vmoffset;
    }
    return -1;
}

int game_init(){
    baseAddr = get_base_address();
    if (baseAddr < 0) {
        return -1;
    }
    return 0;
}

int get_game_task(){
    pid_t pid = get_Pid();
    if (pid < 0){
        return -1;
    }
    kern_return_t kret = task_for_pid(mach_task_self(), pid, &gameTask);
    NSLog(@"[情情]:task:%d::kret:%d", gameTask, kret);
    if (kret == KERN_SUCCESS) {
        NSLog(@"[情情]:get task success:%d", gameTask);
        return gameTask;
    }
    return -1;
}

static kern_return_t read_mem(mach_port_t task, vm_map_offset_t address, mach_vm_size_t size, mach_vm_size_t buffer_bytes, void *buffer)
{
    kern_return_t kert = mach_vm_read_overwrite(task, address, size, (mach_vm_address_t)(buffer), &buffer_bytes); // AAR in Kernel
    return kert;
}

template<typename T> T Read(mach_port_t g_task, long address)
{
    T data;
    read_mem(g_task, address, sizeof(T), sizeof(T), reinterpret_cast<void *>(&data));
    return data;
}

