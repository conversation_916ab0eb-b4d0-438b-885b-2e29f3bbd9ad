//
//  GetTask.h
//  TrollSpeed
//
//  Created by 情情 on 2024/4/1.
//

#ifndef GetTask_h
#define GetTask_h

#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>

#include <mach/mach.h>
#include <mach/vm_map.h>
#include <mach-o/dyld.h>
#include <mach-o/getsect.h>
#include <mach-o/dyld_images.h>
#include <sys/sysctl.h>
#include <dlfcn.h>

typedef void (^PUBGDrawDataFactoryFetchDataBlock)(NSArray *playerArray, CGPoint aimPoint);

@interface GetTask : NSObject

long get_base_address();
int get_game_task();
int game_init();

@end

#endif /* GetTask_h */
