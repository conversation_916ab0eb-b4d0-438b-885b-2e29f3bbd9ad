
#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>

@interface Weapon : NSObject

#pragma mark 步枪
@property (strong,nonatomic) UIImage *M16A4;//M16A4
@property (strong,nonatomic) UIImage *Scar;//Scar-L
@property (strong,nonatomic) UIImage *MK47;//MK47
@property (strong,nonatomic) UIImage *AKM;//AKM
@property (strong,nonatomic) UIImage *QBZ;//QBZ
@property (strong,nonatomic) UIImage *M762;//M762
@property (strong,nonatomic) UIImage *Groza;//<PERSON><PERSON><PERSON>
@property (strong,nonatomic) UIImage *G36C;//G36C
@property (strong,nonatomic) UIImage *AUG;//AUG
@property (strong,nonatomic) UIImage *M416;//M416
@property (strong,nonatomic) UIImage *ACVAL;//AC-VAL
@property (strong,nonatomic) UIImage *mihuan;//蜜獾
#pragma mark 连狙
@property (strong,nonatomic) UIImage *VSS;//VSS
@property (strong,nonatomic) UIImage *MK14;//MK14
@property (strong,nonatomic) UIImage *SKS;//SKS
@property (strong,nonatomic) UIImage *SLR;//SLR
@property (strong,nonatomic) UIImage *Mini14;//Mini14
@property (strong,nonatomic) UIImage *QBU;//QBU
@property (strong,nonatomic) UIImage *M417;//M417
@property (strong,nonatomic) UIImage *MK20;//MK20-H
@property (strong,nonatomic) UIImage *MK12;//MK12
#pragma mark 栓狙
@property (strong,nonatomic) UIImage *Win94;//Win94
@property (strong,nonatomic) UIImage *Kar98k;//Kar98k
@property (strong,nonatomic) UIImage *M24;//M24
@property (strong,nonatomic) UIImage *AWM;//AWM
@property (strong,nonatomic) UIImage *moxinnagan;//莫辛纳甘
@property (strong,nonatomic) UIImage *AMR;//AMR
#pragma mark 冲锋枪
@property (strong,nonatomic) UIImage *UZI;//UZI
@property (strong,nonatomic) UIImage *PP19;//PP19
@property (strong,nonatomic) UIImage *Vector;//Vector
@property (strong,nonatomic) UIImage *Ump45;//Ump45
@property (strong,nonatomic) UIImage *MP5K;//MP5K
@property (strong,nonatomic) UIImage *P90;//P90
@property (strong,nonatomic) UIImage *tomos;//汤姆逊
#pragma mark 霰弹枪
@property (strong,nonatomic) UIImage *S686;//S686
@property (strong,nonatomic) UIImage *S1897;//S1897
@property (strong,nonatomic) UIImage *S12K;//S12K
@property (strong,nonatomic) UIImage *SPAS;//SPAS-12
@property (strong,nonatomic) UIImage *DBS;//DBS
#pragma mark 投掷物
@property (strong,nonatomic) UIImage *Burn;//手雷

@property (strong,nonatomic) UIImage *hand;//空手
@property (strong,nonatomic) UIImage *handgun;//手枪
@property (strong,nonatomic) UIImage *Pan;//Pan

@property (strong,nonatomic) UIImage *M249;//M249
@property (strong,nonatomic) UIImage *DP28;//DP28
@property (strong,nonatomic) UIImage *MG3;//MG3
@property (strong,nonatomic) UIImage *BOW;//弩
@property (strong,nonatomic) UIImage *BurnBOW;//爆炸猎弩

//定义一个初始化单例的方法
+ (instancetype)SharedFuckData;

- (void)initimage;
@end

/*在iOS上使用ImGui绘制图片，你可以使用ImGui的图像函数ImGui::Image()来实现。以下是一个使用ImGui绘制图片的示例代码;
void DrawImage(UIImage* uiImage, ImVec2 size) {
    ImGuiIO& io = ImGui::GetIO();
    
    // 将UIImage转换为ImTextureID
    CGImageRef cgImage = uiImage.CGImage;
    CGFloat width = CGImageGetWidth(cgImage);
    CGFloat height = CGImageGetHeight(cgImage);
    CGColorSpaceRef colorSpace = CGColorSpaceCreateDeviceRGB();
    void* imageData = malloc(width * height * 4);
    CGContextRef context = CGBitmapContextCreate(imageData, width, height, 8, width * 4, colorSpace, kCGImageAlphaPremultipliedLast);
    CGContextDrawImage(context, CGRectMake(0, 0, width, height), cgImage);
    CGColorSpaceRelease(colorSpace);
    CGContextRelease(context);
    CGDataProviderRef dataProvider = CGDataProviderCreateWithData(NULL, imageData, width * height * 4, freeImageData);
    ImTextureID textureID = io.Fonts->GetTexIDFromFontAtlas();
    glBindTexture(GL_TEXTURE_2D, (GLuint)(intptr_t)textureID);
    glTexImage2D(GL_TEXTURE_2D, 0, GL_RGBA, (GLsizei)width, (GLsizei)height, 0, GL_RGBA, GL_UNSIGNED_BYTE, imageData);
    
    // 绘制图片
    ImVec2 pos = ImGui::GetCursorScreenPos();
    ImGui::Image(textureID, size);
    ImGui::GetWindowDrawList()->AddImage(textureID, pos, ImVec2(pos.x + size.x, pos.y + size.y), ImVec2(0, 1), ImVec2(1, 0));
}
你可以在你的ImGui渲染函数中调用DrawImage()函数，传入UIImage对象和预期的尺寸大小即可绘制图片;
*/

