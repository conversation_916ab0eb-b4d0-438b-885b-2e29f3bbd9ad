//
//  Offset.h
//  cross
//
//  Created by hzx on 2022/8/1.
//  Copyright © 2022 drawworld. All rights reserved.
//

namespace Offsets {
long UWorld = 0xB8C3358;
long GNames = 0xB54D538 ;
//long LineOfSightTo_Func = 0x626EE3C;
    
long ULevel = 0x90;
long ActorArray = 0xA0;
long ActorCount = 0xA8;

long GameStateBase = 0x868;//Class: World.Object
long ReplicatedWorldTimeSeconds = 0x56c;//Class: GameStateBase.Info.Actor.Object
long ReplicatedWorldRealTimeSeconds = 0xe88;//Class: STExtraGameStateBase.UAEGameState.GameState.GameStateBase.Info.Actor.Object //世界时间，用来参考手雷时间
    
long rootComponent = 0x268; // Class: Actor.Object。SceneComponent* RootComponent;//[Offset:
long worldPos = 0x1b0;//人物坐标 worldPos
    
long NetDriver = 0x98;//Class: World.Object
long time = 0x148;//Class: NetDriver.Object 进入对局的时间，对局外为0 float Time
long ServerConnection = 0x88;//Class: NetDriver.Object
long PlayerController = 0x30;//Class: Player.Object
long PlayerState = 0x560; //Class: Controller.Actor.Object  di3个
long MySelf = 0x548; //Class: Controller.Actor.Object //Pawn* Pawn;

long TeamID = 0xa70;//Class: UAECharacter.Character.Pawn.Actor.Object

long bIsWeaponFiring = 0x1e30;//开火 bool bIsWeaponFiring;//(ByteOffset: 0, ByteMask: 1, FieldMask: 255)[Offset:
long bIsGunADS = 0x14f8;//开镜  bool bIsGunADS;//(ByteOffset: 0, ByteMask: 1, FieldMask: 255)[Offset

long playerCameraManager = 0x5d0;//Class: PlayerController.Controller.Actor.Object  PlayerCameraManager* PlayerCameraManager;//[Offset:
long WeaponManagerComponent = 0x2a10;//Class: STExtraBaseCharacter.STExtraCharacter.UAECharacter.Character.Pawn.Actor.Object
//CharacterWeaponManagerComponent* WeaponManagerComponent;//[Offset:

#pragma mark - 武器管理第一条指针链
long CachedCurUseWeapon = 0x2d0;//Class: WeaponManagerComponent.ActorComponent.Object
//FString WeaponReloadEffectConfigTablePath;//[Offset:
long ShootWeaponComponent = 0x1178;//Class: STExtraShootWeapon.STExtraWeapon.Actor.Object
//STExtraShootWeaponComponent* ShootWeaponComponent;//[Offset:
long OwnerShootWeapon = 0x168;//Class: STExtraShootWeaponComponent.WeaponLogicBaseComponent.ActorComponent.Object

#pragma mark - 武器管理第二条指针链
long CurrentWeaponReplicated = 0x5d8;
//Class: WeaponManagerComponent.ActorComponent.Object
//STExtraWeapon* CurrentWeaponReplicated
//STExtraWeapon* CurrentWeaponReplicated;//[Offset:

long ShootWeaponEntityComp = 0x13c0;//Class: STExtraShootWeapon.STExtraWeapon.Actor.Object
//    ShootWeaponEntity* ShootWeaponEntityComp;//[Offset:
#pragma mark - Class: ShootWeaponEntity.WeaponEntity.WeaponLogicBaseComponent.ActorComponent.Object
long AccessoriesVRecoilFactor = 0x1738;//垂直后座 float AccessoriesVRecoilFactor;//[Offset:
long AccessoriesHRecoilFactor = 0x1744;//水平后座
long ShotGunVerticalSpread = 0x179c;//垂直散布
long ShotGunHorizontalSpread = 0x17a0;//水平散布
long GameDeviationFactor = 0x17a4;//扩散偏差
long RecoilKickADS = 0x1868;//枪口防抖 float RecoilKickADS;//[Offset:
//long AnimationKick = 0x1864;//镜头防抖
long BulletFireSpeed = 0x1304;//子弹速度
long ShootInterval = 0x18c8;;//武器射速//float ShootInterval;//[Offset:
#pragma mark - END

long LastUpdateStatusKeyList = 0x2c38;//Class: STExtraBaseCharacter.STExtraCharacter.UAECharacter.Character.Pawn.Actor.Object
//AnimStatusKeyList LastUpdateStatusKeyList;//[Offset:
//AnimStatusKeyList LastUpdateStatusKeyList;//[Offset:
long EquipWeapon = 0x20;//Class: AnimStatusKeyList
long RepWeaponID = 0xa90;//Class: STExtraWeapon.Actor.Object   int RepWeaponID;//[Offset:

long ViewTarget = 0x1130; //Class: PlayerCameraManager.Actor.Object。TViewTarget ViewTarget;//[Offset:
long MinimalViewInfo = 0x10; //Class: TViewTarget

long 情情 = 0xde0;//Class: STExtraCharacter.UAECharacter.Character.Pawn.Actor.Object
long QQ2123837096 = 0xdd8;
long bIsAI = 0xa8c;//Class: UAECharacter.Character.Pawn.Actor.Object bool bIsAI;//(ByteOffset: 0, ByteMask: 1, FieldMask: 255)[Offset:
long bDead = 0xe40;//bool bDead;//(ByteOffset: 0, ByteMask: 1, FieldMask: 1)[Offset:
long PlayerName = 0x9f0;
long meshAddr = 0x5c8; //Class: Character.Pawn.Actor.Object
long StaticMesh = 0x6d8; //Class: StaticMeshComponent.MeshComponent.PrimitiveComponent.SceneComponent.ActorComponent.Object
//或者 BodySetup* BodySetup;//[Offset: 0x6f0, Size: 8]？？？
long HighWalkSpeed = 0x2d60;//float HighWalkSpeed;//[Offset:
long VelocitySafety = 0xec4;//Class: STExtraCharacter.UAECharacter.Character.Pawn.Actor.Object  Vector VelocitySafety;//[Offset:
long SpawnTime = 0x6d4;//float SpawnTime;//[Offset:
long ExplosionTime = 0x7ac;//Class: EliteProjectile.Actor.Object
//float ExplosionTime;//[Offset:

long VehicleCommonComponent = 0xa58;//Class: STExtraVehicleBase.Pawn.Actor.Object   VehicleCommonComponent* VehicleCommon;//[Offset:

//LocationLocalSpace 枪口坐标

//PawnStateRepSyncData PawnStateRepSyncData;//[Offset: 0xf90, Size: 16]//人物状态//Class: STExtraCharacter.UAECharacter.Character.Pawn.Actor.Object
}
