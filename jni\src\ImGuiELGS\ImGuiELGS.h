#pragma once
#include <array>
#include <regex>
#include <math.h>
#include <thread>
#include <string>
#include <vector>
#include <sstream>
#include <iomanip>
#include <sys/uio.h>
#include <EGL/egl.h>
#include <GLES2/gl2.h>
#include <sys/syscall.h>
#include <linux/input.h>
#include <linux/uinput.h>
#include <android/native_window_jni.h>
#include "images/head.h"
using namespace std;
#if defined(__aarch64__) && defined(__ANDROID__)
#include "timer.h"
#include "imgui.h"
#include "stb_image.h"
#include "ImGuiTOOL.h"
#include "imgui_internal.h"
#include "imgui_impl_android.h"
#include "imgui_impl_opengl3.h"
#include "NativeWindowCreator.h"

#endif
/*typedef unsigned long long Pointer;
unsigned long G_LineOfSightTo;*/
extern float dx, yuan, zuo, you;
extern float xyd;
extern string ItemName;
extern float ycsz, ycsd, ycsp;
extern bool show556, show762, showRif<PERSON>, showS<PERSON><PERSON><PERSON>e, showSniper, showMirror, showExpansion, showOtherParts, showDrug, showArmor, 空投, 骨灰盒, 地铁;
extern ImTextureID 关;
extern ImTextureID 开;
extern bool 透明, 观透, 首次;
extern bool aigl, dynamic;
extern bool 类名, 无敌炫酷;
extern int 血条;
extern int ArraysCount;
extern int OwnTeam;
extern int SelfAction, 自身动作, 自身武器;
extern int bIsGunADS, IsFire;
extern uintptr_t 基址头;
extern pid_t pid;
extern float Fov;
extern long int WorldAddress, ArrayAddress, SelfAddress, Controller, CameraAddress, RootPoint;
extern float fontScale;
extern double static_ratio;
extern ImColor tempColors[];
extern int SkeletonList_New[][2];
extern int SkeletonList_Old[][2];
extern int SkeletonList_Yan[][2];
extern int SkeletonList_S30[][2];

// POV相关结构体和变量声明
struct FRotator {
    float Pitch;
    float Yaw;
    float Roll;
};

struct MinimalViewInfo {
    VecTor3 Location;
    VecTor3 LocationLocalSpace;
    FRotator Rotation;
    float FOV;
};

extern MinimalViewInfo POV;
class ImGuiELGS : public ImGuiTOOL
{
private:
	bool initializedraw = false;
	bool initializeaimi = true;

	ImVec2 CenterWindow = {0.f, 0.f};
	void WeaponPressureGunValue(int 自身武器, float Fov, bool &isShotgun, bool &isSniper, float &standCrouchPressure, float &pronePressure);
	static string GetTouchScreenDeviceFile();
	static string GetFilePath(const char *name)
	{
		return "/storage/emulated/0/Android/" + string(name);
	}

	VecTor2 GetTouchScreenDimension(int Handle);
	void SaveFile(const char *name)
	{
		ofstream file(GetFilePath(name), ios::binary | ios::out);
		if (file.is_open())
		{
			WriteSaveData(file);
		}
	}

	void LoadFile(const char *name)
	{
		ifstream file(GetFilePath(name), ios::binary | ios::in);
		if (file.is_open())
		{
			ReadsSaveData(file);
		}
	}

	void WriteSaveData(ofstream &file)
	{
		file.write(reinterpret_cast<char *>(&ycsz), sizeof(float));
		file.write(reinterpret_cast<char *>(&ycsd), sizeof(float));
		file.write(reinterpret_cast<char *>(&ycsp), sizeof(float));
		file.write(reinterpret_cast<char *>(&touch_information.TouchRadius), sizeof(float));
		file.write(reinterpret_cast<char *>(&touch_information.TouchPoints), sizeof(VecTor2));
		file.write(reinterpret_cast<char *>(&touch_information.floatswitch), sizeof(float) * 15);
		file.write(reinterpret_cast<char *>(&touch_information.touchLockMode), sizeof(int));
		file.write(reinterpret_cast<char *>(&OneTimeFrame), sizeof(int));
		file.write(reinterpret_cast<char *>(&imguiswitch_information.intswitch), sizeof(int) * 100);
		file.write(reinterpret_cast<char *>(&imguiswitch_information.boolswitch), sizeof(bool) * 100);
		file.write(reinterpret_cast<char *>(&imguiswitch_information.floatswitch), sizeof(float) * 100);
		file.write(reinterpret_cast<char *>(&imguiswitch_information.ProneRecoil), sizeof(float) * 100);
		file.write(reinterpret_cast<char *>(&imguiswitch_information.StandingRecoil), sizeof(float) * 100);
		// 保存颜色数据 - 将 ImColor 分解为 RGBA 分量
		for (int i = 0; i < 24; i++)
		{ // 只保存8个颜色
			float color[4] = {
				imguiswitch_information.colorswitch[i].Value.x,
				imguiswitch_information.colorswitch[i].Value.y,
				imguiswitch_information.colorswitch[i].Value.z,
				imguiswitch_information.colorswitch[i].Value.w};
			file.write(reinterpret_cast<char *>(color), sizeof(float) * 4);
		}
	}

	void ReadsSaveData(ifstream &file)
	{
		file.read(reinterpret_cast<char *>(&ycsz), sizeof(float));
		file.read(reinterpret_cast<char *>(&ycsd), sizeof(float));
		file.read(reinterpret_cast<char *>(&ycsp), sizeof(float));
		file.read(reinterpret_cast<char *>(&touch_information.TouchRadius), sizeof(float));
		file.read(reinterpret_cast<char *>(&touch_information.TouchPoints), sizeof(VecTor2));
		file.read(reinterpret_cast<char *>(&touch_information.floatswitch), sizeof(float) * 15);
		file.read(reinterpret_cast<char *>(&touch_information.touchLockMode), sizeof(int));
		file.read(reinterpret_cast<char *>(&OneTimeFrame), sizeof(int));
		file.read(reinterpret_cast<char *>(&imguiswitch_information.intswitch), sizeof(int) * 100);
		file.read(reinterpret_cast<char *>(&imguiswitch_information.boolswitch), sizeof(bool) * 100);
		file.read(reinterpret_cast<char *>(&imguiswitch_information.floatswitch), sizeof(float) * 100);
		file.read(reinterpret_cast<char *>(&imguiswitch_information.ProneRecoil), sizeof(float) * 100);
		file.read(reinterpret_cast<char *>(&imguiswitch_information.StandingRecoil), sizeof(float) * 100);
		// 读取颜色数据 - 将 RGBA 分量重新组合为 ImColor
		for (int i = 0; i < 24; i++)
		{ // 只读取8个颜色
			float color[4];
			file.read(reinterpret_cast<char *>(color), sizeof(float) * 4);
			imguiswitch_information.colorswitch[i] = ImColor(color[0], color[1], color[2], color[3]);
		}
		// 使用线程延时设置TouchOrientationControl，避免触摸卡顿
		std::thread([this]()
					{
        std::this_thread::sleep_for(std::chrono::milliseconds(100)); // 延时100毫秒
        touch_information.TouchOrientationControl = true; })
			.detach();
	}

	ANativeWindow *native_window = nullptr;
	Android::NativeWindowCreator::DisplayInformation displayinformation;

public:
	string Application_Notice;
	void ImGuiWindowStar();
	void ImGuiWindowMenu();
	void ImGuiWindowDraw();
	void ImGuiWindowExit();
	void ImGuiWindowRele();
	void InitializeFoundationConfiGuration();
	bool InitializeDrawing();

	int OneTimeFrame;
	bool ShutImGuiProcess = true;
	bool ImGuiWindowDisplay = true;

	bool initializetouchorread = true;

	bool AIMIO = false;

	EGLConfig config = nullptr;
	EGLDisplay display = nullptr;
	EGLSurface surface = nullptr;
	EGLContext context = nullptr;
	ImTextureID yeaimi_icon = nullptr;
	ImTextureID noaimi_icon = nullptr;
	ImTextureID texturereadsfile(const unsigned char *buffer, int length);

	ImGuiELGS()
	{
		config = nullptr;
		display = EGL_NO_DISPLAY;
		surface = EGL_NO_SURFACE;
		context = EGL_NO_CONTEXT;
	}

	void ImGuiInItialization();
	bool ImGuiGetSurfaceWindow();
	void ImGuiGetScreenInformation();

	bool WorldTurnScreen(float &Screen, VecTor3 World)
	{
		float Camera = Matrix[0][3] * World.x + Matrix[1][3] * World.y + Matrix[2][3] * World.z + Matrix[3][3];
		if (Camera < 0.03)
		{
			return false;
		}
		Screen = resolution_information.Heiht - (Matrix[0][1] * World.x + Matrix[1][1] * World.y + Matrix[2][1] * World.z + Matrix[3][1]) / Camera * resolution_information.Heiht;
		return true;
	}

	bool WorldTurnScreen(VecTor2 &Screen, VecTor3 World)
	{
		float Camera = Matrix[0][3] * World.x + Matrix[1][3] * World.y + Matrix[2][3] * World.z + Matrix[3][3];
		if (Camera < 0.03)
		{
			return false;
		}
		Screen.x = resolution_information.Width + (Matrix[0][0] * World.x + Matrix[1][0] * World.y + Matrix[2][0] * World.z + Matrix[3][0]) / Camera * resolution_information.Width;
		Screen.y = resolution_information.Heiht - (Matrix[0][1] * World.x + Matrix[1][1] * World.y + Matrix[2][1] * World.z + Matrix[3][1]) / Camera * resolution_information.Heiht;
		return true;
	}
	bool WorldTurnScreen(VecTor2 &Screen, VecTor3 World, bool sig)
	{
		float Camera = Matrix[0][3] * World.x + Matrix[1][3] * World.y + Matrix[2][3] * World.z + Matrix[3][3];
		if (Camera < 0.03 && sig)
		{
			return false;
		}
		Screen.x = resolution_information.Width + (Matrix[0][0] * World.x + Matrix[1][0] * World.y + Matrix[2][0] * World.z + Matrix[3][0]) / Camera * resolution_information.Width;
		Screen.y = resolution_information.Heiht - (Matrix[0][1] * World.x + Matrix[1][1] * World.y + Matrix[2][1] * World.z + Matrix[3][1]) / Camera * resolution_information.Heiht;
		return true;
	}

	bool WorldTurnScreen(VecTor4 &Screen, VecTor3 World)
	{
		float Camera = Matrix[0][3] * World.x + Matrix[1][3] * World.y + Matrix[2][3] * World.z + Matrix[3][3];
		if (Camera < 0.03)
		{
			return false;
		}
		Screen.x = resolution_information.Width + (Matrix[0][0] * World.x + Matrix[1][0] * World.y + Matrix[2][0] * World.z + Matrix[3][0]) / Camera * resolution_information.Width;
		Screen.y = resolution_information.Heiht - (Matrix[0][1] * World.x + Matrix[1][1] * World.y + Matrix[2][1] * World.z + Matrix[3][1]) / Camera * resolution_information.Heiht;
		float ScreenZoom = resolution_information.Heiht - (Matrix[0][1] * World.x + Matrix[1][1] * World.y + Matrix[2][1] * (World.z + 180) + Matrix[3][1]) / Camera * resolution_information.Heiht;
		Screen.z = (Screen.y - ScreenZoom) / 2;
		Screen.w = (Screen.y - ScreenZoom) / 4;
		return true;
	}

	bool WorldTurnScreen(VecTor4 &Screen, float &ScreenCamera, VecTor3 World)
	{
		float Camera = Matrix[0][3] * World.x + Matrix[1][3] * World.y + Matrix[2][3] * World.z + Matrix[3][3];
		ScreenCamera = Matrix[0][3] * World.x + Matrix[1][3] * World.y + Matrix[2][3] * World.z + Matrix[3][3];
		if (Camera < 0.03)
		{
			return false;
		}
		Screen.x = resolution_information.Width + (Matrix[0][0] * World.x + Matrix[1][0] * World.y + Matrix[2][0] * World.z + Matrix[3][0]) / Camera * resolution_information.Width;
		Screen.y = resolution_information.Heiht - (Matrix[0][1] * World.x + Matrix[1][1] * World.y + Matrix[2][1] * World.z + Matrix[3][1]) / Camera * resolution_information.Heiht;
		float ScreenZoom = resolution_information.Heiht - (Matrix[0][1] * World.x + Matrix[1][1] * World.y + Matrix[2][1] * (World.z + 190) + Matrix[3][1]) / Camera * resolution_information.Heiht;
		Screen.z = (Screen.y - ScreenZoom) / 2;
		Screen.w = (Screen.y - ScreenZoom) / 4;
		return true;
	}
	bool WorldTurnScreen(VecTor4 &Screen, float &ScreenCamera, VecTor3 World, bool sig)
	{
		float Camera = Matrix[0][3] * World.x + Matrix[1][3] * World.y + Matrix[2][3] * World.z + Matrix[3][3];
		ScreenCamera = Matrix[0][3] * World.x + Matrix[1][3] * World.y + Matrix[2][3] * World.z + Matrix[3][3];
		if (Camera < 0.03 && sig)
		{
			return false;
		}
		Screen.x = resolution_information.Width + (Matrix[0][0] * World.x + Matrix[1][0] * World.y + Matrix[2][0] * World.z + Matrix[3][0]) / Camera * resolution_information.Width;
		Screen.y = resolution_information.Heiht - (Matrix[0][1] * World.x + Matrix[1][1] * World.y + Matrix[2][1] * World.z + Matrix[3][1]) / Camera * resolution_information.Heiht;
		float ScreenZoom = resolution_information.Heiht - (Matrix[0][1] * World.x + Matrix[1][1] * World.y + Matrix[2][1] * (World.z + 190) + Matrix[3][1]) / Camera * resolution_information.Heiht;
		Screen.z = (Screen.y - ScreenZoom) / 2;
		Screen.w = (Screen.y - ScreenZoom) / 4;
		return true;
	}

	// 添加RotatorToMatrix函数
	FMatrix RotatorToMatrix(FRotator rotation) {
		float radPitch = rotation.Pitch * ((float) M_PI / 180.0f);
		float radYaw = rotation.Yaw * ((float) M_PI / 180.0f);
		float radRoll = rotation.Roll * ((float) M_PI / 180.0f);

		float SP = sinf(radPitch);
		float CP = cosf(radPitch);
		float SY = sinf(radYaw);
		float CY = cosf(radYaw);
		float SR = sinf(radRoll);
		float CR = cosf(radRoll);

		FMatrix matrix;

		matrix.M[0][0] = (CP * CY);
		matrix.M[0][1] = (CP * SY);
		matrix.M[0][2] = (SP);
		matrix.M[0][3] = 0;

		matrix.M[1][0] = (SR * SP * CY - CR * SY);
		matrix.M[1][1] = (SR * SP * SY + CR * CY);
		matrix.M[1][2] = (-SR * CP);
		matrix.M[1][3] = 0;

		matrix.M[2][0] = (-(CR * SP * CY + SR * SY));
		matrix.M[2][1] = (CY * SR - CR * SP * SY);
		matrix.M[2][2] = (CR * CP);
		matrix.M[2][3] = 0;

		matrix.M[3][0] = 0;
		matrix.M[3][1] = 0;
		matrix.M[3][2] = 0;
		matrix.M[3][3] = 1;

		return matrix;
	}

	// 新增基于POV的WorldToScreen函数实现
	VecTor2 WorldToScreenPOV(VecTor3 worldLocation) {
		// 使用RotatorToMatrix函数创建旋转矩阵
		FMatrix tempMatrix = RotatorToMatrix(POV.Rotation);

		VecTor3 vAxisX(tempMatrix.M[0][0], tempMatrix.M[0][1], tempMatrix.M[0][2]);
		VecTor3 vAxisY(tempMatrix.M[1][0], tempMatrix.M[1][1], tempMatrix.M[1][2]);
		VecTor3 vAxisZ(tempMatrix.M[2][0], tempMatrix.M[2][1], tempMatrix.M[2][2]);

		VecTor3 vDelta = worldLocation - POV.Location;

		// 计算点积 - 使用与参考代码相同的顺序
		float dotY = vDelta.x * vAxisY.x + vDelta.y * vAxisY.y + vDelta.z * vAxisY.z;
		float dotZ = vDelta.x * vAxisZ.x + vDelta.y * vAxisZ.y + vDelta.z * vAxisZ.z;
		float dotX = vDelta.x * vAxisX.x + vDelta.y * vAxisX.y + vDelta.z * vAxisX.z;

		VecTor3 vTransformed(dotY, dotZ, dotX);

		if (vTransformed.z < 1.0f) {
			vTransformed.z = 1.0f;
		}

		float halfsw = resolution_information.Width / 2.0f;
		float halfsh = resolution_information.Heiht / 2.0f;
		float fmpi = (float)(M_PI / 360.0f);

		VecTor2 result = VecTor2(
			(halfsw + vTransformed.x * (halfsw / tanf(POV.FOV * fmpi)) / vTransformed.z),
			(halfsh - vTransformed.y * (halfsw / tanf(POV.FOV * fmpi)) / vTransformed.z)
		);

		// 调试输出坐标转换过程
		static int debugCount = 0;
		if (debugCount++ % 100 == 0) { // 每100次输出一次，避免日志过多
			LOGI("世界坐标转换: 世界(%.1f,%.1f,%.1f) -> 屏幕(%.1f,%.1f)",
				worldLocation.x, worldLocation.y, worldLocation.z, result.x, result.y);
			LOGI("变换后坐标: (%.2f,%.2f,%.2f), FOV=%.1f",
				vTransformed.x, vTransformed.y, vTransformed.z, POV.FOV);
		}

		return result;
	}

	bool WorldToScreenPOV(VecTor2 &Screen, VecTor3 World) {
		VecTor2 result = WorldToScreenPOV(World);
		Screen = result;
		// 简单的屏幕边界检查
		return (result.x >= 0 && result.x <= resolution_information.Width * 2 &&
				result.y >= 0 && result.y <= resolution_information.Heiht * 2);
	}

	bool WorldToScreenPOV(VecTor4 &Screen, float &ScreenCamera, VecTor3 World) {
		VecTor2 result = WorldToScreenPOV(World);
		Screen.x = result.x;
		Screen.y = result.y;

		// 计算相机距离
		VecTor3 vDelta = World - POV.Location;
		ScreenCamera = sqrt(vDelta.x * vDelta.x + vDelta.y * vDelta.y + vDelta.z * vDelta.z);

		// 计算屏幕缩放
		VecTor3 zoomWorld = World;
		zoomWorld.z += 190;
		VecTor2 zoomResult = WorldToScreenPOV(zoomWorld);
		Screen.z = (Screen.y - zoomResult.y) / 2;
		Screen.w = (Screen.y - zoomResult.y) / 4;

		return (result.x >= 0 && result.x <= resolution_information.Width * 2 &&
				result.y >= 0 && result.y <= resolution_information.Heiht * 2);
	}

	bool WorldToScreenPOV(float &Screen, VecTor3 World) {
		VecTor2 result = WorldToScreenPOV(World);
		Screen = result.y;
		return (result.x >= 0 && result.x <= resolution_information.Width * 2 &&
				result.y >= 0 && result.y <= resolution_information.Heiht * 2);
	}

	void OffScreen(VecTor2 Obj, float camear, ImColor color, float Radius)
	{
		ImRect screen_rect = {0.0f, 0.0f, static_cast<float>(resolution_information.Width * 2), static_cast<float>(resolution_information.Heiht * 2)};
		auto screen_center = screen_rect.GetCenter();
		auto angle = atan2(screen_center.y - Obj.y, screen_center.x - Obj.x);
		angle += camear > 0 ? M_PI : 0.0f;
		VecTor2 arrow_center{
			screen_center.x + Radius * cosf(angle),
			screen_center.y + Radius * sinf(angle)};
		std::array<ImVec2, 4> points{
			ImVec2(-22.0f, -8.6f),
			ImVec2(0.0f, 0.0f),
			ImVec2(-22.0f, 8.6f),
			ImVec2(-18.0f, 0.0f)};
		for (auto &point : points)
		{
			auto x = point.x * 1.155f;
			auto y = point.y * 1.155f;
			point.x = arrow_center.x + x * cosf(angle) - y * sinf(angle);
			point.y = arrow_center.y + x * sinf(angle) + y * cosf(angle);
		}
		float alpha = 1.0f;
		if (camear > 0)
		{
			constexpr float nearThreshold = 200 * 200;
			ImVec2 screen_outer_diff = {
				Obj.x < 0 ? abs(Obj.x) : (Obj.x > screen_rect.Max.x ? Obj.x - screen_rect.Max.x : 0.0f),
				Obj.y < 0 ? abs(Obj.y) : (Obj.y > screen_rect.Max.y ? Obj.y - screen_rect.Max.y : 0.0f),
			};
			float distance = static_cast<float>(pow(screen_outer_diff.x, 2) + pow(screen_outer_diff.y, 2));
			alpha = camear < 0 ? 1.0f : (distance / nearThreshold);
		}
		ImColor arrowColor = color;
		arrowColor.Value.w = std::min(alpha, 1.0f);
		ImGui::GetBackgroundDrawList()->AddTriangleFilled(points[0], points[1], points[3], arrowColor);
		ImGui::GetBackgroundDrawList()->AddTriangleFilled(points[2], points[1], points[3], arrowColor);
		ImGui::GetBackgroundDrawList()->AddQuad(points[0], points[1], points[2], points[3], ImColor(0.0f, 0.0f, 0.0f, alpha), 1.335f);
	}

	void BezierCurve(ImVec2 p1, ImVec2 p2, ImVec2 p3, ImVec2 p4, int segments, vector<ImVec2> &points)
	{
		for (int i = 0; i <= segments; ++i)
		{
			float t = (float)i / (float)segments;
			float u = 1 - t;
			float tt = t * t;
			float uu = u * u;
			float uuu = uu * u;
			float ttt = tt * t;
			ImVec2 p = ImVec2(uuu * p1.x, uuu * p1.y); // first term
			p.x += 3 * uu * t * p2.x;
			p.y += 3 * uu * t * p2.y; // second term
			p.x += 3 * u * tt * p3.x;
			p.y += 3 * u * tt * p3.y; // third term
			p.x += ttt * p4.x;
			p.y += ttt * p4.y; // fourth term
			points.push_back(p);
		}
	}

	void ExplosionRange(VecTor3 Obj, ImColor color, float Range, float thickn)
	{
		VecTor3 l1, l2, l3, l4, l5, l6, l7, l8;
		VecTor2 lw1, lw2, lw3, lw4, lw5, lw6, lw7, lw8;
		l1 = VecTor3(Obj.x - Range, Obj.y - Range, Obj.z);
		l2 = VecTor3(Obj.x, Obj.y - Range, Obj.z);
		l3 = VecTor3(Obj.x + Range, Obj.y - Range, Obj.z);
		l4 = VecTor3(Obj.x - Range, Obj.y, Obj.z);
		l5 = VecTor3(Obj.x + Range, Obj.y, Obj.z);
		l6 = VecTor3(Obj.x - Range, Obj.y + Range, Obj.z);
		l7 = VecTor3(Obj.x, Obj.y + Range, Obj.z);
		l8 = VecTor3(Obj.x + Range, Obj.y + Range, Obj.z);
		this->WorldToScreenPOV(lw1, l1);
		this->WorldToScreenPOV(lw2, l2);
		this->WorldToScreenPOV(lw3, l3);
		this->WorldToScreenPOV(lw4, l4);
		this->WorldToScreenPOV(lw5, l5);
		this->WorldToScreenPOV(lw6, l6);
		this->WorldToScreenPOV(lw7, l7);
		this->WorldToScreenPOV(lw8, l8);

		// 绘制曲线
		ImGui::GetBackgroundDrawList()->AddBezierCubic({lw4.x, lw4.y}, {lw1.x, lw1.y}, {lw2.x, lw2.y}, {lw2.x, lw2.y}, color, thickn);
		ImGui::GetBackgroundDrawList()->AddBezierCubic({lw2.x, lw2.y}, {lw3.x, lw3.y}, {lw5.x, lw5.y}, {lw5.x, lw5.y}, color, thickn);
		ImGui::GetBackgroundDrawList()->AddBezierCubic({lw5.x, lw5.y}, {lw8.x, lw8.y}, {lw7.x, lw7.y}, {lw7.x, lw7.y}, color, thickn);
		ImGui::GetBackgroundDrawList()->AddBezierCubic({lw7.x, lw7.y}, {lw6.x, lw6.y}, {lw4.x, lw4.y}, {lw4.x, lw4.y}, color, thickn);
	}
	void ExplosionRangeFilled(VecTor3 Obj, ImColor color, float Range, int num_segments)
	{
		VecTor3 l1, l2, l3, l4, l5, l6, l7, l8;
		VecTor2 lw1, lw2, lw3, lw4, lw5, lw6, lw7, lw8;
		l1 = VecTor3(Obj.x - Range, Obj.y - Range, Obj.z);
		l2 = VecTor3(Obj.x, Obj.y - Range, Obj.z);
		l3 = VecTor3(Obj.x + Range, Obj.y - Range, Obj.z);
		l4 = VecTor3(Obj.x - Range, Obj.y, Obj.z);
		l5 = VecTor3(Obj.x + Range, Obj.y, Obj.z);
		l6 = VecTor3(Obj.x - Range, Obj.y + Range, Obj.z);
		l7 = VecTor3(Obj.x, Obj.y + Range, Obj.z);
		l8 = VecTor3(Obj.x + Range, Obj.y + Range, Obj.z);
		this->WorldToScreenPOV(lw1, l1);
		this->WorldToScreenPOV(lw2, l2);
		this->WorldToScreenPOV(lw3, l3);
		this->WorldToScreenPOV(lw4, l4);
		this->WorldToScreenPOV(lw5, l5);
		this->WorldToScreenPOV(lw6, l6);
		this->WorldToScreenPOV(lw7, l7);
		this->WorldToScreenPOV(lw8, l8);

		vector<ImVec2> circle_points;

		// 生成曲线上的点
		BezierCurve({lw4.x, lw4.y}, {lw1.x, lw1.y}, {lw2.x, lw2.y}, {lw2.x, lw2.y}, num_segments, circle_points);
		BezierCurve({lw2.x, lw2.y}, {lw3.x, lw3.y}, {lw5.x, lw5.y}, {lw5.x, lw5.y}, num_segments, circle_points);
		BezierCurve({lw5.x, lw5.y}, {lw8.x, lw8.y}, {lw7.x, lw7.y}, {lw7.x, lw7.y}, num_segments, circle_points);
		BezierCurve({lw7.x, lw7.y}, {lw6.x, lw6.y}, {lw4.x, lw4.y}, {lw4.x, lw4.y}, num_segments, circle_points);

		// 绘制填充
		ImGui::GetBackgroundDrawList()->AddConvexPolyFilled(circle_points.data(), (int)circle_points.size(), color);
	}

	float GetDistance(VecTor3 Object, VecTor3 Self)
	{
		float DistanceX = pow(Object.x - Self.x, 2);
		float DistanceY = pow(Object.y - Self.y, 2);
		float DistanceZ = pow(Object.z - Self.z, 2);
		return sqrt(DistanceX + DistanceY + DistanceZ);
	}

	void GetDistance(VecTor3 Object, VecTor3 Self, float &Distance)
	{
		float DistanceX = pow(Object.x - Self.x, 2);
		float DistanceY = pow(Object.y - Self.y, 2);
		float DistanceZ = pow(Object.z - Self.z, 2);
		Distance = sqrt(DistanceX + DistanceY + DistanceZ) / 100;
	}

	void GetUTF8(char *Buffer, uintptr_t Point)
	{
		read(Point, BUFFER16, 28);
		char *TempUTF8 = Buffer;
		char *UTF8End = TempUTF8 + 32;
		unsigned short *TempUTF16 = BUFFER16;
		while (TempUTF16 < TempUTF16 + 28)
		{
			if (*TempUTF16 <= 0x007F && TempUTF8 + 1 < UTF8End)
			{
				*TempUTF8++ = (char)*TempUTF16;
			}
			else if (*TempUTF16 >= 0x0080 && *TempUTF16 <= 0x07FF && TempUTF8 + 2 < UTF8End)
			{
				*TempUTF8++ = (*TempUTF16 >> 6) | 0xC0;
				*TempUTF8++ = (*TempUTF16 & 0x3F) | 0x80;
			}
			else if (*TempUTF16 >= 0x0800 && *TempUTF16 <= 0xFFFF && TempUTF8 + 3 < UTF8End)
			{
				*TempUTF8++ = (*TempUTF16 >> 12) | 0xE0;
				*TempUTF8++ = ((*TempUTF16 >> 6) & 0x3F) | 0x80;
				*TempUTF8++ = (*TempUTF16 & 0x3F) | 0x80;
			}
			else
			{
				break;
			}
			TempUTF16++;
		}
	}

	string GetClassName(uintptr_t Address, uintptr_t Number)
	{
		char ClassName[128] = "";
		unsigned int Index = read<int>(Address + Number);
		unsigned int Block = (Index / 0x4000) * 0x8;
		unsigned int Offset = (Index % 0x4000) * 0x8;
		uintptr_t NamePool = read<uintptr_t>(ModulesBase[0] + 0x11EFDCA0);
		uintptr_t NamePoolChunk = read<uintptr_t>(NamePool + Block);
		read(read<uintptr_t>(NamePoolChunk + Offset) + 0xC, &ClassName, sizeof(ClassName));
		return string(ClassName);
	}

	VecTor2 WorldToScreen(VecTor3 obj, float matrix[16], float ViewW)
	{
		float x = resolution_information.Width + (matrix[0] * obj.x + matrix[4] * obj.y + matrix[8] * obj.z + matrix[12]) / ViewW * resolution_information.Width;
		float y = resolution_information.Heiht - (matrix[1] * obj.x + matrix[5] * obj.y + matrix[9] * obj.z + matrix[13]) / ViewW * resolution_information.Heiht;
		return VecTor2(x, y);
	}

	Transform getBone(unsigned long addr)
	{
		Transform transform;
		read(addr, &transform, 4 * 11);
		return transform;
	}

	FMatrix TransformToMatrix(Transform transform)
	{
		FMatrix matrix;
		matrix.M[3][0] = transform.Translation.x;
		matrix.M[3][1] = transform.Translation.y;
		matrix.M[3][2] = transform.Translation.z;
		float x2 = transform.Rotation.x + transform.Rotation.x;
		float y2 = transform.Rotation.y + transform.Rotation.y;
		float z2 = transform.Rotation.z + transform.Rotation.z;
		float xx2 = transform.Rotation.x * x2;
		float yy2 = transform.Rotation.y * y2;
		float zz2 = transform.Rotation.z * z2;
		matrix.M[0][0] = (1 - (yy2 + zz2)) * transform.Scale3D.x;
		matrix.M[1][1] = (1 - (xx2 + zz2)) * transform.Scale3D.y;
		matrix.M[2][2] = (1 - (xx2 + yy2)) * transform.Scale3D.z;
		float yz2 = transform.Rotation.y * z2;
		float wx2 = transform.Rotation.w * x2;
		matrix.M[2][1] = (yz2 - wx2) * transform.Scale3D.z;
		matrix.M[1][2] = (yz2 + wx2) * transform.Scale3D.y;
		float xy2 = transform.Rotation.x * y2;
		float wz2 = transform.Rotation.w * z2;
		matrix.M[1][0] = (xy2 - wz2) * transform.Scale3D.y;
		matrix.M[0][1] = (xy2 + wz2) * transform.Scale3D.x;
		float xz2 = transform.Rotation.x * z2;
		float wy2 = transform.Rotation.w * y2;
		matrix.M[2][0] = (xz2 + wy2) * transform.Scale3D.z;
		matrix.M[0][2] = (xz2 - wy2) * transform.Scale3D.x;
		matrix.M[0][3] = 0;
		matrix.M[1][3] = 0;
		matrix.M[2][3] = 0;
		matrix.M[3][3] = 1;
		return matrix;
	}

	FMatrix MatrixMulti(FMatrix m1, FMatrix m2)
	{
		FMatrix matrix = FMatrix();
		for (int i = 0; i < 4; i++)
		{
			for (int j = 0; j < 4; j++)
			{
				for (int k = 0; k < 4; k++)
				{
					matrix.M[i][j] += m1.M[i][k] * m2.M[k][j];
				}
			}
		}
		return matrix;
	}
	VecTor3 MarixToVector(FMatrix matrix)
	{
		return VecTor3(matrix.M[3][0], matrix.M[3][1], matrix.M[3][2]);
	}

	void ReadBone(uintptr_t Address, Transform &Result)
	{
		read(Address, &Result.Rotation, 16);
		read(Address + 0x20, &Result.Scale3D, 12);
		read(Address + 0x10, &Result.Translation, 12);
	}

	void TransformTurnMatrix(FMatrix &Matrix, Transform Result)
	{
		Matrix.M[3][0] = Result.Translation.x;
		Matrix.M[3][1] = Result.Translation.y;
		Matrix.M[3][2] = Result.Translation.z;
		float RotationX = Result.Rotation.x + Result.Rotation.x;
		float RotationY = Result.Rotation.y + Result.Rotation.y;
		float RotationZ = Result.Rotation.z + Result.Rotation.z;
		float RotationXX = Result.Rotation.x * RotationX;
		float RotationYY = Result.Rotation.y * RotationY;
		float RotationZZ = Result.Rotation.z * RotationZ;
		Matrix.M[0][0] = 1 - (RotationYY + RotationZZ) * Result.Scale3D.x;
		Matrix.M[1][1] = 1 - (RotationXX + RotationZZ) * Result.Scale3D.y;
		Matrix.M[2][2] = 1 - (RotationXX + RotationYY) * Result.Scale3D.z;
		float RotationYZ = Result.Rotation.y * RotationZ;
		float RotationWX = Result.Rotation.w * RotationX;
		Matrix.M[2][1] = (RotationYZ - RotationWX) * Result.Scale3D.z;
		Matrix.M[1][2] = (RotationYZ + RotationWX) * Result.Scale3D.y;
		float RotationXY = Result.Rotation.x * RotationY;
		float RotationWZ = Result.Rotation.w * RotationZ;
		Matrix.M[1][0] = (RotationXY - RotationWZ) * Result.Scale3D.y;
		Matrix.M[0][1] = (RotationXY + RotationWZ) * Result.Scale3D.x;
		float RotationXZ = Result.Rotation.x * RotationZ;
		float RotationWY = Result.Rotation.w * RotationY;
		Matrix.M[2][0] = (RotationXZ + RotationWY) * Result.Scale3D.z;
		Matrix.M[0][2] = (RotationXZ - RotationWY) * Result.Scale3D.x;
		Matrix.M[0][3] = 0;
		Matrix.M[1][3] = 0;
		Matrix.M[2][3] = 0;
		Matrix.M[3][3] = 1;
	}

	void MatrixMulti(VecTor3 &World, FMatrix Matrix_a, FMatrix Matrix_b, FMatrix Matrix_c)
	{
		memset(&Matrix_a.M[0][0], 0, sizeof(Matrix_a.M));
		for (int i = 0; i < 4; i++)
		{
			for (int u = 0; u < 4; u++)
			{
				Matrix_a.M[i][u] += Matrix_b.M[i][0] * Matrix_c.M[0][u];
				Matrix_a.M[i][u] += Matrix_b.M[i][1] * Matrix_c.M[1][u];
				Matrix_a.M[i][u] += Matrix_b.M[i][2] * Matrix_c.M[2][u];
				Matrix_a.M[i][u] += Matrix_b.M[i][3] * Matrix_c.M[3][u];
			}
		}
		World.x = Matrix_a.M[3][0];
		World.y = Matrix_a.M[3][1];
		World.z = Matrix_a.M[3][2];
	}

	void GetBoneTransform(int Count, VecTor2 &WorldBone)
	{
		VecTor3 BoneCoordinates = {0, 0, 0};
		ReadBone(BoneAddress + Count * 0x30, MeshTran);
		TransformTurnMatrix(BoneMatrix, MeshTran);
		MatrixMulti(BoneCoordinates, OutcMatrix, BoneMatrix, XMatrix);
		this->WorldToScreenPOV(WorldBone, BoneCoordinates);
	}

	void GetBoneTransform(int Count, VecTor3 &BoneCoordinates, VecTor2 &WorldBone)
	{
		ReadBone(BoneAddress + Count * 0x30, MeshTran);
		TransformTurnMatrix(BoneMatrix, MeshTran);
		MatrixMulti(BoneCoordinates, OutcMatrix, BoneMatrix, XMatrix);
		if (Count == 5)
		{
			BoneCoordinates.z += 5;
		}
		this->WorldToScreenPOV(WorldBone, BoneCoordinates);
	}

	void GetRadarCoordinates(VecTor2 &Radar, float Angle, VecTor3 Enemy, VecTor3 Self, float Zoom)
	{
		float s = sin((Angle - 90) * M_PI / 180);
		float c = cos((Angle - 90) * M_PI / 180);
		float RadarX = (Self.x - Enemy.x) / Zoom;
		float RadarY = (Self.y - Enemy.y) / Zoom;
		Radar.x = RadarX * c + RadarY * s, Radar.y = -RadarX * s + RadarY * c;
	}

	bool CapTivity(float Range, VecTor2 Pos)
	{
		float RadiusX = abs(resolution_information.Width + Pos.x - resolution_information.Width);
		float RadiusY = abs(resolution_information.Heiht + Pos.y - resolution_information.Heiht);
		if (sqrt(RadiusX * RadiusX + RadiusY * RadiusY) <= Range)
		{
			return true;
		}
		return false;
	}
};
bool 获取枪械信息(int 枪械编码, ImTextureID *图片名称);
bool isPartialMatchedType(int type, const std::string &itemName, std::string &matchedName);
ImTextureID ImAgeHeadFile(const unsigned char *buf, int len);
void miaobian(float size, int x, int y, ImVec4 color, const char *str);
void 绘制加粗文本(float size, float x, float y, ImColor color, ImColor color1, const char *str);
void DrawPlayerBox(ImDrawList *Draw, float left, float right, float bottom, float top, float x, float y, ImColor color, float size);
bool 识别喷子(int WeaponId);