//
//  MsUIViewController.m
//
//  Created by 梦三大牛 on 2022/12/22.
//
//

#import "MsHomeMenuController.h"
#import "MsdnMetalDraw.h"
@implementation MsHomeMenuController
MsHomeMenuController *HomeMenuController;
#pragma mark - 视图
- (void)viewDidLoad {
    [super viewDidLoad];
    //==============初始化Metal绘制===================
    Global_DrawView = [[sdkafowanbnonowaf alloc] initWithFrame:self.view.frame];
    Global_DrawView.device = MTLCreateSystemDefaultDevice();
    Global_DrawView.preferredFramesPerSecond = 120;
    g_renderer = [[MsRenderer alloc] initWithMetalKitView:Global_DrawView];
    Global_DrawView.delegate = g_renderer;
    [Global_DrawView setBackgroundColor:[UIColor clearColor]];

    //==============初始化过直播视图===============
    UITextField *MsZhiBoMetalView = [[UITextField alloc]initWithFrame:Global_DrawView.frame];
    MsZhiBoMetalView.backgroundColor = [UIColor clearColor];
    MsZhiBoMetalView.secureTextEntry = NO;
    MsZhiBoMetalView.userInteractionEnabled = YES;
    [MsZhiBoMetalView.subviews.firstObject addSubview:Global_DrawView];//添加Metal视图
    [self.view addSubview:MsZhiBoMetalView];
}

#pragma mark - 事件
- (BOOL)_ignoresHitTest {
    return YES;
}

- (BOOL)shouldAutorotate{
    return NO;
}
- (UIInterfaceOrientationMask)supportedInterfaceOrientations {
    return UIDeviceOrientationLandscapeRight;
}

- (UIInterfaceOrientation)preferredInterfaceOrientationForPresentation {
    return UIInterfaceOrientationLandscapeRight;
}

@end

