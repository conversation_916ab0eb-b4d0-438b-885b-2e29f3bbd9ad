
#import "DrawView.h"
#import "UdpDataManager.h"
#import "Struct.h"
#import "InfoView.h"
#import "OInfoView.h"
#import "ReadData.h"
#define SCREEN_WIDTH [UIScreen mainScreen].bounds.size.width
#define SCREEN_HEIGHT [UIScreen mainScreen].bounds.size.height
static DrawView *drawview = nil;
@interface DrawView()
{
    UdpDataManager *udp;
    ReadData *data;
    float screen_width;
    float screen_height;
}
@property (nonatomic,strong)NSMutableArray *ViewArray;
@property (assign)NSUInteger count;
@property (nonatomic,  strong) CATextLayer *numberlayer;

@property (nonatomic,  strong) CAShapeLayer *WhiteboneLayer;
@property (nonatomic,  strong) CAShapeLayer *YellowboneLayer;
@property (nonatomic,  strong) CAShapeLayer *GreenboneLayer;
@property (nonatomic,  strong) CAShapeLayer *redray;
@property (nonatomic,  strong) CAShapeLayer *BlueLayer;

@property (nonatomic,  strong) NSArray *tempdata;
@end

@implementation DrawView

#pragma mark -------------------------------------视图-------------------------------------------
//这里开始
+ (instancetype)SharedDrawView
{
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
         if (!drawview) {
              drawview = [[DrawView alloc] initWithFrame:[UIScreen mainScreen].bounds];
           }
    });
     return drawview;
}

- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {
        self.userInteractionEnabled = NO;
        self.layer.backgroundColor = [UIColor clearColor].CGColor;
        self.ViewArray = [NSMutableArray array];
        [self.layer addSublayer:self.WhiteboneLayer];
        [self.layer addSublayer:self.YellowboneLayer];
        [self.layer addSublayer:self.GreenboneLayer];
        [self.layer addSublayer:self.BlueLayer];
        [self.layer addSublayer:self.redray];
        [self.layer addSublayer:self.numberlayer];
        self.hidden = NO;
        udp = [UdpDataManager Share];
        data = [ReadData share];
        screen_width = SCREEN_WIDTH>SCREEN_HEIGHT?SCREEN_WIDTH:SCREEN_HEIGHT;
        screen_height = SCREEN_WIDTH<SCREEN_HEIGHT?SCREEN_WIDTH:SCREEN_HEIGHT;
        self.FpsTimer = [CADisplayLink displayLinkWithTarget:self selector:@selector(ReadData)];
        [self.FpsTimer addToRunLoop:[NSRunLoop currentRunLoop] forMode:NSRunLoopCommonModes];
    }
    return self;
}
#pragma mark -------------------------------------事件-------------------------------------------
- (void)RemoveViewArray
{
    [self.subviews makeObjectsPerformSelector:@selector(removeFromSuperview)];
    [_ViewArray removeAllObjects];
}
- (void)initdrawsubview
{
    [self RemoveViewArray];
    for (NSInteger i = 0; i < _count; ++i) {
        OInfoView *infoview = [[OInfoView alloc]init];
        infoview.hidden = true;
        [self addSubview:infoview];
        [_ViewArray addObject:infoview];
    }
}
-(void)clear:(NSUInteger)viewcount
{
    int i = 0;
    for (InfoView *subview in _ViewArray) {
        i++;
        if(i>viewcount||i==viewcount)subview.hidden = true;
    }
}
- (void)ReadData
{
    __block bool temp = false;
    [[ReadData share]ReturnData:^(NSArray * _Nonnull data) {
        temp = true;
        self->_tempdata = data;
        [self drawAction];
    }];
    if(temp == false)
    {
        self.hidden = true;
    }else
    {
        self.hidden = false;
    }
}
//开始绘制
- (void)drawAction
{
    if(!_tempdata||_tempdata.count==0)return;
    
    if(_tempdata.count>_count)
    {
        _count = _tempdata.count;
        [self initdrawsubview];
    }
    [self clear:_tempdata.count];
    
    int count = 0;
    
    UIBezierPath *blueray = [UIBezierPath bezierPath];
    
    for (NSInteger i = 0; i < _tempdata.count; ++i) {
        NSData *aData = [_tempdata objectAtIndex:i];
        ActorInfo  aStruct;
        [aData getBytes:&aStruct length:sizeof(aStruct)];
        
        OInfoView *subview = _ViewArray[i];
        subview.hidden = false;
        
        if(aStruct.type == 1)
        {
            subview.team = -1;
            subview.dis = aStruct.Distance;
            subview.xue = aStruct.HP;
            subview.name = @"";
            subview.WeaponID = 602004;
            subview.center = CGPointMake(aStruct.X-50, aStruct.Y+12);
            UIBezierPath *line = [UIBezierPath bezierPath];
            [line moveToPoint:CGPointMake(screen_width*0.5, screen_height*0.5)];
            [line addLineToPoint:CGPointMake(aStruct.X, aStruct.Y)];
            continue;
        }
        if(aStruct.type == 6)
        {
            subview.team = -1;
            subview.dis = aStruct.Distance;
            subview.xue = 0;
            subview.name = [NSString stringWithCString:aStruct.name.c_str()encoding:NSUTF8StringEncoding];
            subview.WeaponID = 0;
            subview.center = CGPointMake(aStruct.X-50, aStruct.Y+12);
            continue;
        } 
        count++;
        subview.team = aStruct.Team;
        subview.dis = aStruct.Distance;
        subview.xue = aStruct.HP;
        subview.name = [NSString stringWithCString:aStruct.name.c_str() encoding:NSUTF8StringEncoding];
        subview.WeaponID = aStruct.WeaponID;
//        subview.center = CGPointMake(aStruct.X-50, aStruct.Y-7);
        //============LLLL============
//        subview.nameLb.text = @"11111";
        NSLog(@"[情情]:[name]:%s:name:%@", __func__, [NSString stringWithCString:aStruct.name.c_str() encoding:NSUTF8StringEncoding]);
        [subview setName:[NSString stringWithCString:aStruct.name.c_str() encoding:NSUTF8StringEncoding]];
        subview.center = CGPointMake(aStruct.X-50, aStruct.Y+13);
        subview.arrowLb.text = @"▼";
        if(aStruct.IsAi){
            subview.nameLb.textColor = [UIColor greenColor];
            subview.arrowLb.textColor = [UIColor greenColor];
            subview.disLb.textColor = [UIColor greenColor];
        } else {
            subview.nameLb.textColor = [UIColor whiteColor];
            subview.arrowLb.textColor = [UIColor whiteColor];
            subview.disLb.textColor = [UIColor whiteColor];
        }
        
        subview.disLb.hidden = !udp.guge;
        subview.arrowLb.hidden = !udp.guge;
        subview.teamLb.hidden = YES;
        subview.infoBar.hidden = YES;
        subview.xueBar.hidden = YES;
        subview.weapon.hidden = YES;
        subview.delta.hidden = YES;
        //============LLLL============
    }
    if(udp.zhimiaoquan>0)
    {
        CGPoint zhimiaoCenter = CGPointMake(screen_width*0.5, screen_height*0.5);
        UIBezierPath *circle;
        if(udp.aimquantype)
            circle = [UIBezierPath bezierPathWithArcCenter:zhimiaoCenter radius:udp.Radius startAngle:0 endAngle:M_PI * 2  clockwise:YES];
        else
            circle = [UIBezierPath bezierPathWithArcCenter:zhimiaoCenter radius:udp.zhimiaoquan startAngle:0 endAngle:M_PI * 2  clockwise:YES];
        
        [blueray appendPath:circle];
        [blueray appendPath:data.RedLayer];
    }
    _numberlayer.string = @(count).stringValue;
    self.BlueLayer.path = blueray.CGPath;
    self.YellowboneLayer.path = data.YellowLayer.CGPath;
    self.GreenboneLayer.path = data.GreenLayer.CGPath;
    self.WhiteboneLayer.path = data.WhiteLayer.CGPath;
    [data.YellowLayer removeAllPoints];
    [data.GreenLayer removeAllPoints];
    [data.WhiteLayer removeAllPoints];
    [data.RedLayer removeAllPoints];
}

#pragma mark -------------------------------------懒加载-----------------------------------------

//人数
- (CATextLayer *)numberlayer{
    if (!_numberlayer) {
        CATextLayer *shapeLayer = [CATextLayer layer];
        //shapeLayer.frame = self.bounds;
        if(SCREEN_WIDTH>SCREEN_HEIGHT)shapeLayer.frame = CGRectMake(SCREEN_WIDTH/2-15, 25, 30, 22);
        else{shapeLayer.frame = CGRectMake(SCREEN_HEIGHT/2-15, 25, 30, 22);}
        shapeLayer.string = @"";
        shapeLayer.foregroundColor = [UIColor redColor].CGColor;
        
        UIFont *font = [UIFont boldSystemFontOfSize:22];
        CFStringRef fontName = (__bridge CFStringRef)font.fontName;
        CGFontRef fontRef = CGFontCreateWithFontName(fontName);
        shapeLayer.font = fontRef;
        shapeLayer.fontSize = font.pointSize;
        shapeLayer.contentsScale = [UIScreen mainScreen].scale;
        _numberlayer = shapeLayer;
    }
    return _numberlayer;
}

//红射线
- (CAShapeLayer *)redray{
    if (!_redray) {
        CAShapeLayer *shapeLayer = [CAShapeLayer layer];
        shapeLayer.frame = self.bounds;
        shapeLayer.strokeColor = [UIColor redColor].CGColor;
        shapeLayer.fillColor = [UIColor clearColor].CGColor;
        shapeLayer.lineWidth = 0.5;
        _redray = shapeLayer;
    }
    return _redray;
}
//白色骨骼
- (CAShapeLayer *)WhiteboneLayer{
    if (!_WhiteboneLayer) {
        CAShapeLayer *shapeLayer = [CAShapeLayer layer];
        shapeLayer.frame = self.bounds;
        shapeLayer.strokeColor = [UIColor colorWithRed: 255 green: 255 blue: 255 alpha: 1.0].CGColor;//射线颜色
        shapeLayer.fillColor = [UIColor clearColor].CGColor;//必须添加，否则无法触屏
        shapeLayer.lineWidth = 0.5;//射线宽度
        _WhiteboneLayer = shapeLayer;
    }
    return _WhiteboneLayer;
}

//黄色骨骼
- (CAShapeLayer *)YellowboneLayer{
    if (!_YellowboneLayer) {
        CAShapeLayer *shapeLayer = [CAShapeLayer layer];
        shapeLayer.frame = self.bounds;
//        shapeLayer.strokeColor = [UIColor colorWithRed: 1 green: 1 blue: 0 alpha: 1.80].CGColor;//射线颜色
        shapeLayer.strokeColor = [UIColor greenColor].CGColor;//改绿色
        shapeLayer.fillColor = [UIColor clearColor].CGColor;//必须添加，否则无法触屏
//        shapeLayer.lineWidth = 1.0;//射线宽度
        shapeLayer.lineWidth = 0.5;//射线宽度
        _YellowboneLayer = shapeLayer;
    }
    return _YellowboneLayer;
}

//绿色骨骼
- (CAShapeLayer *)GreenboneLayer{
    if (!_GreenboneLayer) {
        CAShapeLayer *shapeLayer = [CAShapeLayer layer];
        shapeLayer.frame = self.bounds;
        shapeLayer.strokeColor  =[UIColor colorWithRed: 0 green: 1 blue: 0 alpha: 1.0].CGColor;//射线颜色
        shapeLayer.fillColor = [UIColor clearColor].CGColor;//必须添加，否则无法触屏
        shapeLayer.lineWidth = 0.5;//射线宽度
        _GreenboneLayer = shapeLayer;
    }
    return _GreenboneLayer;
}

- (CAShapeLayer *)BlueLayer{
    if (!_BlueLayer) {
        CAShapeLayer *shapeLayer = [CAShapeLayer layer];
        shapeLayer.frame = self.bounds;
        shapeLayer.strokeColor  =[UIColor colorWithRed: 0 green: 221 blue: 255 alpha: 1.00].CGColor;//射线颜色
        shapeLayer.fillColor = [UIColor clearColor].CGColor;//必须添加，否则无法触屏
        shapeLayer.lineWidth = 0.3;//射线宽度
        _BlueLayer = shapeLayer;
    }
    return _BlueLayer;
}

@end
