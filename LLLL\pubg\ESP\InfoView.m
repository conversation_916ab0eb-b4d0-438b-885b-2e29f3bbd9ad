

#import "InfoView.h"
#import "Weapon.h"

@interface InfoView()

@end

@implementation InfoView

-(instancetype)initWithFrame:(CGRect)frame
{
    self=[super initWithFrame:frame];
    if (self) {
        self.backgroundColor = [UIColor clearColor];
        self.userInteractionEnabled = NO;
        
        UIFont *font12 = [UIFont boldSystemFontOfSize:12];
        CFStringRef fontName12 = (__bridge CFStringRef)font12.fontName;
        CGFontRef fontRef12 = CGFontCreateWithFontName(fontName12);
        
        UIFont *font15 = [UIFont boldSystemFontOfSize:15];
        CFStringRef fontName15 = (__bridge CFStringRef)font15.fontName;
        CGFontRef fontRef15 = CGFontCreateWithFontName(fontName15);
        
        _distext = [CATextLayer layer];
        _distext.frame = CGRectMake(20, 0, 100, 20);
        _distext.foregroundColor = [UIColor greenColor].CGColor;
        _distext.font = fontRef12;
        _distext.fontSize = font12.pointSize;
        _distext.contentsScale = [UIScreen mainScreen].scale;
        [self.layer addSublayer:_distext];
        
        _hptext = [CATextLayer layer];
        _hptext.frame = CGRectMake(63, 2, 100, 20);
        _hptext.foregroundColor = [UIColor greenColor].CGColor;
        _hptext.font = fontRef12;
        _hptext.fontSize = font12.pointSize;
        _hptext.contentsScale = [UIScreen mainScreen].scale;
        [self.layer addSublayer:_hptext];
        
        _nametext = [CATextLayer layer];
        _nametext.frame = CGRectMake(50, 16, 120, 15);
        _nametext.foregroundColor = [UIColor greenColor].CGColor;
        _nametext.font = fontRef12;
        _nametext.fontSize = font12.pointSize;
        _nametext.contentsScale = [UIScreen mainScreen].scale;
        [self.layer addSublayer:_nametext];
        
        _teamtext = [CATextLayer layer];
        _teamtext.frame = CGRectMake(20, 15, 80, 20);
        _teamtext.foregroundColor = [UIColor greenColor].CGColor;
        _teamtext.font = fontRef15;
        _teamtext.fontSize = font15.pointSize;
        _teamtext.contentsScale = [UIScreen mainScreen].scale;
        [self.layer addSublayer:_teamtext];
        
        //_weapon = [[UIImageView alloc]initWithFrame:CGRectMake(-5, -62, 110, 90)];
        _weapon = [[UIImageView alloc]initWithFrame:CGRectMake(0, 0, 55, 45)];
        //_weapon = [[UIImageView alloc]init];
        _weapon.center = CGPointMake(50,-12);
        _weapon.backgroundColor = [UIColor clearColor];
        //_weapon.contentMode = UIViewContentModeScaleAspectFit;
        _weapon.contentMode = UIViewContentModeCenter;
        //_weapon.image = [Weapon SharedFuckData].M16A4;
        [self addSubview:_weapon];
        
    }
    return self;
}
#pragma mark--功能性方法
-(UIColor*)colorWithHex:(NSUInteger)hex
                  alpha:(CGFloat)alpha
{
    float r, g, b, a;
    a = alpha;
    b = hex & 0x0000FF;
    hex = hex >> 8;
    g = hex & 0x0000FF;
    hex = hex >> 8;
    r = hex;

    return [UIColor colorWithRed:r/255.0f
                           green:g/255.0f
                           blue:b/255.0f
                           alpha:a];
}

#pragma mark--给属性重新赋值
-(void)setDis:(CGFloat)dis
{
    if(dis!=0)
    {
    _distext.string = [NSString stringWithFormat:@"[%d米]",(int)dis];
    }else {_distext.string = @"";}
}

-(void)setName:(NSString *)name
{
    if(![name  isEqual: @""])
    {
    _nametext.string = [NSString stringWithFormat:@"%@",name];
    }else{_nametext.string = @"";}
}

-(void)setTeam:(int)team
{
    NSUInteger colorArray[] = {0x00BFFF};
    UIColor *color;
    if(team==-1)
    {_teamtext.string=@"";}
    else if(team == 0)
    {
        color = [self colorWithHex:0xFFFFFF alpha:1]; //alpha:0.6
        _teamtext.string=[NSString stringWithFormat:@"AI"];
    }else
    {
        color = [self colorWithHex:colorArray[team % (sizeof(colorArray)/8)] alpha:1];
        _teamtext.string = [NSString stringWithFormat:@"%d",team];
    }
    _teamtext.foregroundColor = color.CGColor;
}

-(void)setXue:(CGFloat)xue
{
    if(xue != 0)
    {
    _hptext.string = [NSString stringWithFormat:@"%d",(int)xue];
        }else{_hptext.string = @"";}
}
-(void)setWeaponID:(int)WeaponID
{
    /*
    if(WeaponID==0){_WeaponLb.text = [NSString stringWithFormat:@"[空手]"];}
    else if(WeaponID==666){_WeaponLb.text = [NSString stringWithFormat:@"[空手]"];}
    else if(WeaponID==101001){_WeaponLb.text = [NSString stringWithFormat:@"[AKM]"];}
    else if(WeaponID==101002){_WeaponLb.text = [NSString stringWithFormat:@"[M16A4]"];}
    else if(WeaponID==101003){_WeaponLb.text = [NSString stringWithFormat:@"[SCAR-L]"];}
    else if(WeaponID==101004){_WeaponLb.text = [NSString stringWithFormat:@"[M416]"];}
    else if(WeaponID==101005){_WeaponLb.text = [NSString stringWithFormat:@"[GROZA]"];}
    else if(WeaponID==101006){_WeaponLb.text = [NSString stringWithFormat:@"[AUG]"];}
    else if(WeaponID==101007){_WeaponLb.text = [NSString stringWithFormat:@"[QBZ]"];}
    else if(WeaponID==101008){_WeaponLb.text = [NSString stringWithFormat:@"[M762]"];}
    else if(WeaponID==101009){_WeaponLb.text = [NSString stringWithFormat:@"[MK47]"];}
    else if(WeaponID==101010){_WeaponLb.text = [NSString stringWithFormat:@"[G36C]"];}
    else if(WeaponID==101011){_WeaponLb.text = [NSString stringWithFormat:@"[AC-VAL]"];}
    else if(WeaponID==101012){_WeaponLb.text = [NSString stringWithFormat:@"[蜜獾]"];}
    else if(WeaponID==102001){_WeaponLb.text = [NSString stringWithFormat:@"[UZI]"];}
    else if(WeaponID==102002){_WeaponLb.text = [NSString stringWithFormat:@"[UMP45]"];}
    else if(WeaponID==102003){_WeaponLb.text = [NSString stringWithFormat:@"[Vector]"];}
    else if(WeaponID==102004){_WeaponLb.text = [NSString stringWithFormat:@"[汤姆逊]"];}
    else if(WeaponID==102005){_WeaponLb.text = [NSString stringWithFormat:@"[野牛]"];}
    else if(WeaponID==102007){_WeaponLb.text = [NSString stringWithFormat:@"[MP5K]"];}
    else if(WeaponID==102105){_WeaponLb.text = [NSString stringWithFormat:@"[P90]"];}
    else if(WeaponID==103001){_WeaponLb.text = [NSString stringWithFormat:@"[Kar98K]"];}
    else if(WeaponID==103002){_WeaponLb.text = [NSString stringWithFormat:@"[M24]"];}
    else if(WeaponID==103003){_WeaponLb.text = [NSString stringWithFormat:@"[AWM]"];}
    else if(WeaponID==103004){_WeaponLb.text = [NSString stringWithFormat:@"[SKS]"];}
    else if(WeaponID==103005){_WeaponLb.text = [NSString stringWithFormat:@"[VSS]"];}
    else if(WeaponID==103006){_WeaponLb.text = [NSString stringWithFormat:@"[Mini14]"];}
    else if(WeaponID==103007){_WeaponLb.text = [NSString stringWithFormat:@"[MK14]"];}
    else if(WeaponID==103008){_WeaponLb.text = [NSString stringWithFormat:@"[Win94]"];}
    else if(WeaponID==103009){_WeaponLb.text = [NSString stringWithFormat:@"[SLR]"];}
    else if(WeaponID==103010){_WeaponLb.text = [NSString stringWithFormat:@"[QBU]"];}
    else if(WeaponID==103011){_WeaponLb.text = [NSString stringWithFormat:@"[莫辛纳甘]"];}
    else if(WeaponID==103012){_WeaponLb.text = [NSString stringWithFormat:@"[AMR]"];}
    else if(WeaponID==103013){_WeaponLb.text = [NSString stringWithFormat:@"[M417]"];}
    else if(WeaponID==104001){_WeaponLb.text = [NSString stringWithFormat:@"[S686]"];}
    else if(WeaponID==104002){_WeaponLb.text = [NSString stringWithFormat:@"[S1897]"];}
    else if(WeaponID==104003){_WeaponLb.text = [NSString stringWithFormat:@"[S12K]"];}
    else if(WeaponID==104004){_WeaponLb.text = [NSString stringWithFormat:@"[DBS]"];}
    else if(WeaponID==104100){_WeaponLb.text = [NSString stringWithFormat:@"[SPAS-12]"];}
    else if(WeaponID==105001){_WeaponLb.text = [NSString stringWithFormat:@"[M249]"];}
    else if(WeaponID==105002){_WeaponLb.text = [NSString stringWithFormat:@"[DP-28]"];}
    else if(WeaponID==105010){_WeaponLb.text = [NSString stringWithFormat:@"[MG3]"];}
    else if(WeaponID==107001){_WeaponLb.text = [NSString stringWithFormat:@"[十字弩]"];}
    else if(WeaponID==107007){_WeaponLb.text = [NSString stringWithFormat:@"[爆炸猎弓]"];}
    else if(WeaponID==602004){_WeaponLb.text = [NSString stringWithFormat:@"[手雷]"];}
    else if(WeaponID==108001||WeaponID==108002||WeaponID==108003||WeaponID==108004){_WeaponLb.text = [NSString stringWithFormat:@"[近战武器]"];}
    else if(WeaponID==106005||WeaponID==106006||WeaponID==106008||WeaponID==106010){_WeaponLb.text = [NSString stringWithFormat:@"[手枪]"];}
    else{_WeaponLb.text = [NSString stringWithFormat:@"[空手]"];}*/
    /*
    if(WeaponID!=0){
    NSData *imageData = [[NSData alloc] initWithBase64EncodedString:@m16a4
                                                            options:NSDataBase64DecodingIgnoreUnknownCharacters];
    UIImage *image = [UIImage imageWithData:imageData];
    _weapon.image = image;
        
    }*/
    //_weapon.image = [Weapon SharedFuckData].M16A4;
    if(WeaponID==0){_weapon.image = NULL;}
    else if(WeaponID==666){_weapon.image = NULL;}
    else if(WeaponID==101001){_weapon.image = [Weapon SharedFuckData].AKM;}
    else if(WeaponID==101002){_weapon.image = [Weapon SharedFuckData].M16A4;}
    else if(WeaponID==101003){_weapon.image = [Weapon SharedFuckData].Scar;}
    else if(WeaponID==101004){_weapon.image = [Weapon SharedFuckData].M416;}
    else if(WeaponID==101005){_weapon.image = [Weapon SharedFuckData].Groza;}
    else if(WeaponID==101006){_weapon.image = [Weapon SharedFuckData].AUG;}
    else if(WeaponID==101007){_weapon.image = [Weapon SharedFuckData].QBZ;}
    else if(WeaponID==101008){_weapon.image = [Weapon SharedFuckData].M762;}
    else if(WeaponID==101009){_weapon.image = [Weapon SharedFuckData].MK47;}
    else if(WeaponID==101010){_weapon.image = [Weapon SharedFuckData].G36C;}
    else if(WeaponID==101011){_weapon.image = [Weapon SharedFuckData].ACVAL;}
    else if(WeaponID==101012){_weapon.image = [Weapon SharedFuckData].mihuan;}
    else if(WeaponID==102001){_weapon.image = [Weapon SharedFuckData].UZI;}
    else if(WeaponID==102002){_weapon.image = [Weapon SharedFuckData].Ump45;}
    else if(WeaponID==102003){_weapon.image = [Weapon SharedFuckData].Vector;}
    else if(WeaponID==102004){_weapon.image = [Weapon SharedFuckData].tomos;}
    else if(WeaponID==102005){_weapon.image = [Weapon SharedFuckData].PP19;}
    else if(WeaponID==102007){_weapon.image = [Weapon SharedFuckData].MP5K;}
    else if(WeaponID==102105){_weapon.image = [Weapon SharedFuckData].P90;}
    else if(WeaponID==103001){_weapon.image = [Weapon SharedFuckData].Kar98k;}
    else if(WeaponID==103002){_weapon.image = [Weapon SharedFuckData].M24;}
    else if(WeaponID==103003){_weapon.image = [Weapon SharedFuckData].AWM;}
    else if(WeaponID==103004){_weapon.image = [Weapon SharedFuckData].SKS;}
    else if(WeaponID==103005){_weapon.image = [Weapon SharedFuckData].VSS;}
    else if(WeaponID==103006){_weapon.image = [Weapon SharedFuckData].Mini14;}
    else if(WeaponID==103007){_weapon.image = [Weapon SharedFuckData].MK14;}
    else if(WeaponID==103008){_weapon.image = [Weapon SharedFuckData].Win94;}
    else if(WeaponID==103009){_weapon.image = [Weapon SharedFuckData].SLR;}
    else if(WeaponID==103010){_weapon.image = [Weapon SharedFuckData].QBU;}
    else if(WeaponID==103011){_weapon.image = [Weapon SharedFuckData].moxinnagan;}//莫辛纳甘
    else if(WeaponID==103012){_weapon.image = [Weapon SharedFuckData].AMR;}
    else if(WeaponID==103013){_weapon.image = [Weapon SharedFuckData].M417;}
    else if(WeaponID==103014){_weapon.image = [Weapon SharedFuckData].MK20;}
    //else if(WeaponID==103100){_weapon.image = [Weapon SharedFuckData].MK12;}
    else if(WeaponID==104001){_weapon.image = [Weapon SharedFuckData].S686;}
    else if(WeaponID==104002){_weapon.image = [Weapon SharedFuckData].S1897;}
    else if(WeaponID==104003){_weapon.image = [Weapon SharedFuckData].S12K;}
    else if(WeaponID==104004){_weapon.image = [Weapon SharedFuckData].DBS;}
    else if(WeaponID==104100){_weapon.image = [Weapon SharedFuckData].SPAS;}
    else if(WeaponID==105001){_weapon.image = [Weapon SharedFuckData].M249;}
    else if(WeaponID==105002){_weapon.image = [Weapon SharedFuckData].DP28;}
    else if(WeaponID==105010){_weapon.image = [Weapon SharedFuckData].MG3;}
    else if(WeaponID==107001){_weapon.image = [Weapon SharedFuckData].BOW;}
    else if(WeaponID==107007){_weapon.image = [Weapon SharedFuckData].BurnBOW;}
    else if(WeaponID==602004){_weapon.image = [Weapon SharedFuckData].Burn;}
    else if(WeaponID==108001||WeaponID==108002||WeaponID==108003||WeaponID==108004){_weapon.image = [Weapon SharedFuckData].Pan;}
    else if(WeaponID==106005||WeaponID==106006||WeaponID==106008||WeaponID==106010){_weapon.image = [Weapon SharedFuckData].handgun;}
    else{_weapon.image = NULL;}
}

@end
