# ImGuiDraw.cpp 矩阵方法改为ViewTarget+MinimalViewInfo方法

## 修改概述
将原来使用矩阵的WorldTurnScreen方法改为使用ViewTarget + MinimalViewInfo的方法，参考LLLL\pubg\Header\ReadData\ReadData.mm中的实现。

## 主要修改内容

### 1. 添加POV相关结构体和变量 (ImGuiDraw.cpp)
```cpp
// 添加POV相关结构体和变量
struct FRotator {
    float Pitch;
    float Yaw;
    float Roll;
};

struct MinimalViewInfo {
    VecTor3 Location;
    VecTor3 LocationLocalSpace;
    FRotator Rotation;
    float FOV;
};

MinimalViewInfo POV;
float halfsw, halfsh, fmpi;
```

### 2. 添加基于POV的WorldToScreen函数
```cpp
// 添加基于POV的WorldToScreen函数
VecTor2 WorldToScreenPOV(VecTor3 worldLocation);
bool WorldToScreenPOV(VecTor2 &Screen, VecTor3 World);
bool WorldToScreenPOV(VecTor4 &Screen, float &ScreenCamera, VecTor3 World);
bool WorldToScreenPOV(float &Screen, VecTor3 World);
```

### 3. 修改相机管理器获取方式
```cpp
// 获取playerCameraManager用于ViewTarget方法
uintptr_t playerCameraManager = GetPointer(Controller, 0x608, 0);
```

### 4. 添加POV数据读取
```cpp
// 读取POV数据 - 使用ViewTarget + MinimalViewInfo方法
if (playerCameraManager != 0) {
    // ViewTarget坐标是1330，MinimalViewInfo是0x10
    read(playerCameraManager + 1330 + 0x10, &POV, sizeof(POV));
    // 单独读取FOV，确保正确性
    read(playerCameraManager + 1330 + 0x10 + 0x30, &POV.FOV, sizeof(POV.FOV));
}
```

### 5. 初始化POV相关变量
```cpp
// 初始化POV相关变量
halfsw = resolution_information.Width / 2.0f;
halfsh = resolution_information.Heiht / 2.0f;
fmpi = (float)(M_PI / 360.0f);
```

### 6. 替换Matrix读取为POV方法
```cpp
// 注释掉原来的Matrix读取，改用POV方法
// if (read(MatrixAddress, &Matrix[0][0], 64))
// {
//     touch_information.Scal = sqrt(Matrix[0][0] * Matrix[0][0] + Matrix[1][0] * Matrix[1][0] + Matrix[2][0] * Matrix[2][0]);
// }
// 使用POV方法时，可以用距离来计算缩放
VecTor3 deltaPos = ObjectCoordinate - POV.Location;
touch_information.Scal = sqrt(deltaPos.x * deltaPos.x + deltaPos.y * deltaPos.y + deltaPos.z * deltaPos.z) / 100.0f;
```

### 7. 替换所有WorldTurnScreen调用为WorldToScreenPOV
- 骨骼坐标转换
- 主要屏幕坐标转换
- 敌背绘制坐标转换
- 物品绘制坐标转换

## 头文件修改 (ImGuiELGS.h)

### 1. 添加函数声明
```cpp
// 新增基于POV的WorldToScreen函数声明
VecTor2 WorldToScreenPOV(VecTor3 worldLocation);
bool WorldToScreenPOV(VecTor2 &Screen, VecTor3 World);
bool WorldToScreenPOV(VecTor4 &Screen, float &ScreenCamera, VecTor3 World);
bool WorldToScreenPOV(float &Screen, VecTor3 World);
```

### 2. 更新ExplosionRange和ExplosionRangeFilled函数
将其中的WorldTurnScreen调用替换为WorldToScreenPOV调用。

### 3. 更新GetBoneTransform函数
将其中的WorldTurnScreen调用替换为WorldToScreenPOV调用。

## 关键参数
- ViewTarget坐标: 1330
- MinimalViewInfo偏移: 0x10
- FOV偏移: 0x30 (相对于MinimalViewInfo)

## 优势
1. 不再依赖矩阵读取，减少了一次内存读取操作
2. 使用更直接的相机视图信息进行坐标转换
3. 与参考代码LLLL\pubg\Header\ReadData\ReadData.mm保持一致的实现方式
4. 提高了坐标转换的准确性和稳定性

## 注意事项
1. 确保playerCameraManager地址正确
2. ViewTarget和MinimalViewInfo的偏移值需要根据实际游戏版本调整
3. POV结构体的内存布局需要与游戏内部结构保持一致
