

#ifndef MemoryTool_h
#define MemoryTool_h

#import <mach-o/dyld.h>
#import <mach/mach.h>
#import <mach/vm_map.h>
#import <dlfcn.h>

#import <sys/mman.h>

#import "UdpDataManager.h"

#pragma mark
inline bool IsValidAddress(long addr) {
    return addr > 0x100000000 && addr < 0x1600000000;
}
void oldread(long addr, void *buffer, int len)
{
    if (!IsValidAddress(addr)) return;
    vm_size_t size = 0;
    vm_read_overwrite(mach_task_self(), (vm_address_t)addr, len, (vm_address_t)buffer, &size);
}
bool ReadSelfTaskMemory(vm_address_t addr, void *buf, unsigned long len)
{
    vm_size_t dataCnt = len;
    kern_return_t kr = vm_read_overwrite(mach_task_self(), addr, len, (vm_address_t)buf, (vm_size_t *)&dataCnt);
    if (kr)
        return false;
    if (len != dataCnt)
    {
        return false;
    }
    return true;
}
bool ReadMemoryByTask(mach_port_t task, vm_address_t addr, void *buf, unsigned long len)
{
    vm_size_t dataCnt = len;
    kern_return_t kr = vm_read_overwrite(task, addr, len, (vm_address_t)buf, (vm_size_t *)&dataCnt);
    if (kr)
        return false;
    if (len != dataCnt)
    {
        return false;
    }
    return true;
}
void oldwrite(long addr, void *buffer, int len)
{
    if (!IsValidAddress(addr)) return;
    vm_write(mach_task_self(), (vm_address_t)addr, (vm_offset_t)buffer, (mach_msg_type_number_t)len);
}
template<typename T> T VM_Read(long address) {
    T data;
    oldread(address, reinterpret_cast<void *>(&data), sizeof(T));
    return data;
}
template<typename T> void VM_Write(long address, T data) {
    oldwrite(address, reinterpret_cast<void *>(&data), sizeof(T));
}

template<typename T> T FastRead(long address) {
    T data;
    if(!IsValidAddress(address))return data;
    memcpy(&data, reinterpret_cast<void *>(address), sizeof(T));
    return data;
}
#pragma mark 指针--------------------------------------------------------------------------------
unsigned long ReadPtr(long address) {
    unsigned long data = 0;
    if(!IsValidAddress(address))return data;
    memcpy(&data, reinterpret_cast<void *>(address), 8);
    return data;
}
#pragma mark int--------------------------------------------------------------------------------
int Readint(long address) {
    int data = 0;
    if(!IsValidAddress(address))return data;
    memcpy(&data, reinterpret_cast<void *>(address), 4);
    return data;
}
#pragma mark unsigned int--------------------------------------------------------------------------------
unsigned int ReadUint(long address) {
    unsigned int data = 0;
    if(!IsValidAddress(address))return data;
    memcpy(&data, reinterpret_cast<void *>(address), 4);
    return data;
}
#pragma mark float--------------------------------------------------------------------------------
float ReadFloat(long address) {
    float data = 0;
    if(!IsValidAddress(address))return data;
    memcpy(&data, reinterpret_cast<void *>(address), 4);
    return data;
}
#pragma mark long--------------------------------------------------------------------------------
long ReadLong(long address) {
    long data = 0;
    if(!IsValidAddress(address))return data;
    memcpy(&data, reinterpret_cast<void *>(address), 8);
    return data;
}
#pragma mark bool--------------------------------------------------------------------------------
bool ReadBool(long address) {
    bool data = 0;
    if(!IsValidAddress(address))return data;
    memcpy(&data, reinterpret_cast<void *>(address), 1);
    return data;
}

void FastWrite(uintptr_t address, size_t size, void *buffer) {
    if (address <= 0x100000000 || address >= 0x2000000000) {
        return ;
    }
    memcpy((void *) address, buffer, size);
}
bool getType(unsigned int data)
{
int a = data & 0xffff8000;
int b = a + 0x00008000;
int c = b & 0xffff7fff;
return c;
}
bool WriteData(long long address,  unsigned int data) {
    kern_return_t err;
    mach_port_t port = mach_task_self();
    err = vm_protect(port, (long long) address, sizeof(data), NO,VM_PROT_READ | VM_PROT_WRITE | VM_PROT_COPY);
    if (err != KERN_SUCCESS) {
       return FALSE;
    }
    if(getType(data))
    {
        data = CFSwapInt32(data);
        err = vm_write(port, address, (long long)&data, sizeof(data));
    }
    else
    {
        data = (unsigned short)data;
        data = CFSwapInt16(data);
        err = vm_write(port, address, (long long)&data, sizeof(data));
    }
    if (err != KERN_SUCCESS) {
    return FALSE;
    }
    err = vm_protect(port, (long long)address, sizeof(data), NO,VM_PROT_READ | VM_PROT_EXECUTE);
      return TRUE;
}
#endif /* MemoryTool_h */
