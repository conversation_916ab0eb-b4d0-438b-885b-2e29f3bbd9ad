
#import <UIKit/UIKit.h>

@interface InfoView : UIView
@property(nonatomic,retain)UIImageView *weapon;
@property(nonatomic,retain)CATextLayer *nametext;
@property(nonatomic,retain)CATextLayer *hptext;
@property(nonatomic,retain)CATextLayer *teamtext;
@property(nonatomic,retain)CATextLayer *distext;

@property(nonatomic,retain)NSString *name;
@property(nonatomic)CGFloat dis;
@property(nonatomic)CGFloat xue;
@property(nonatomic)int team;
@property(nonatomic)int WeaponID;


-(UIColor *)colorWithHex:(NSUInteger)hex
                     alpha:(CGFloat)alpha;

@end
