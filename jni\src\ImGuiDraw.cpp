#include <vector>
#include <iomanip> //cout输出16进制hex
#include <sstream> // 添加对 std::stringstream 的支持
#include "ImGuiELGS.h"
#include "touch.h"
#include "ImGuidraft.h"
#include <unordered_set>
#include <android/log.h>
#include "imgui.h" // 添加ImGui头文件
// 添加外部变量声明
extern int mode;
#define LOG_TAG "MyApp" // 定义日志标签
#define LOGI(...) ((void)__android_log_print(ANDROID_LOG_INFO, LOG_TAG, __VA_ARGS__))
int (*skeletonList)[2] = nullptr;
string ItemName;
touch touchdriven;
const int max_obj_number = 500;
int scid;
ImTextureID 图片;
ImVec4 BoneColor;
ImVec4 BoxColor;
ImVec4 LineColor;
float deltaTime = 0.0f;		// 帧间隔时间
float lastFrameTime = 0.0f; // 上一帧的时间
struct SHARED_INFORMATION Shared_Information[max_obj_number];
struct THROW_INFORMATION Throw_Information[max_obj_number];
struct VEHICLE_INFORMATION Vehicle_Information[max_obj_number];
bool IsGameStart;
int aimitar = -1;
int aiminur = -1;
float aimi_abs = 0;
float aimi_x_abs = 0;
float aimi_y_abs = 0;
bool aimibot = false;
bool aimifal = false;
bool aimidage = false;
string aiminame = "";
float aimidistance = 0;
bool aimiswitch = false;
struct VecTor3 aimista = {0, 0, 0};
struct VecTor3 aimiend = {0, 0, 0};
struct VecTor3 aimiant = {0, 0, 0};

// POV变量定义
MinimalViewInfo POV;
int OBJECTCOUNT[2] = {0, 0};
float LeftFoot_Skeleton = 0;
float RihtFoot_Skeleton = 0;
struct VecTor3 HeadSkeleton = {0, 0, 0};
struct VecTor3 SelfCoordinate = {0, 0, 0};
struct VecTor3 LeftFootSkeleton = {0, 0, 0};
struct VecTor3 RihtFootSkeleton = {0, 0, 0};
struct VecTor3 ObjectCoordinate = {0, 0, 0};
struct VecTor3 SelfViewCoordinate = {0, 0, 0};
static float initialThrowTime = 0.0f; // 记录掐雷开始的时间
static bool isHoldingGrenade = false; // 是否正在掐雷
float offsetRangeX = 20.0f;
float offsetRangeY = 20.0f;
VecTor3 VelocitySafety;
float bulletSpeed;
float bulletFlyTime;
float AccessoriesVRecoilFactor;
float objectHeight;
bool YuCurrentVehicle;
int smokeCount = 0;					   // 用于存储烟雾数量
std::vector<VecTor3> smokeCoordinates; // 用于存储烟雾坐标
float AimDistance;
float minDistance = FLT_MAX;
int excludeTeam = -1;
const float MAX_SEARCH_DISTANCE = 15.0f;
float LastRenderTime = 0;
float LastRenderTime2 = 0;
char lt[100];
const int BONE_SCOPED = 5;
const int BONE_HIPFIRE = 4;
const int BONE_SHOTGUN = 2;
int AIMBONE = 5;
bool isSpecialWeaponFire = false;
float RandomOffset(float range)
{
	return (rand() / (float)RAND_MAX) * range * 2 - range; // 生成 [-range, range] 的随机值
}

// POV函数现在在ImGuiELGS类中实现，通过this指针调用
void ImGuiELGS::ImGuiWindowDraw()
{
	if (initializetouchorread)
	{
		InitializeColor();
		if (mode == 1)
		{
			thread *touchinformationthread = new thread([this]
														{ touchdriven.GetTouch(&touch_information, &resolution_information, AIMIO); });
			touchinformationthread->detach();
		}
		else if (mode == 2)
		{
			initializedraw = InitializeDrawing();
		}
		initializetouchorread = false;
	}
	ImDrawList *drawlist = ImGui::GetForegroundDrawList();
	if (initializedraw && Pid > 0)
	{
		IsGameStart = GetPointer(ModulesBase[0], 0x12161358, 0x98, 0);
		if (IsGameStart)
		{
			if (imguiswitch_information.boolswitch[8])
			{
				drawlist->AddCircleFilled({resolution_information.Width + imguiswitch_information.floatswitch[1], resolution_information.Heiht + imguiswitch_information.floatswitch[2]}, imguiswitch_information.floatswitch[4], ImColor(10, 10, 10, 80));
				drawlist->AddCircle({resolution_information.Width + imguiswitch_information.floatswitch[1], resolution_information.Heiht + imguiswitch_information.floatswitch[2]}, imguiswitch_information.floatswitch[4], ImColor(255, 255, 255, 255), 0, 2);
				drawlist->AddCircle({resolution_information.Width + imguiswitch_information.floatswitch[1], resolution_information.Heiht + imguiswitch_information.floatswitch[2]}, imguiswitch_information.floatswitch[4] / 2, ImColor(255, 255, 255, 255), 0, 2);
			}
			uintptr_t Controller = GetPointer(ModulesBase[0], 0x12161358, 0x98, 0x88, 0x30, 0);
			uintptr_t CameraAddress = GetPointer(Controller, 0x608, 0);
			uintptr_t MatrixAddress = GetPointer(ModulesBase[0], 0x12132D60, 0x20, 0x270);
			// 按照参考代码获取PlayerCameraManager
			uintptr_t PlayerCameraManager = read<uintptr_t>(Controller + 0x398);
			// uintptr_t Matrix2 = GetPointer(ModulesBase[0], 0x10715E70, 0x98, 0x740);
			uintptr_t SelfAddress = read<uintptr_t>(Controller + 0x32a8);
			WorldAddress = GetPointer(ModulesBase[0], 0x12161358, 0x90, 0);
			ArrayAddress = GetPointer(WorldAddress, 0xa0, 0);
			ArraysCount = read<int>(WorldAddress + 0xa8);
			if (WorldAddress != 0 || Controller != 0 || ArrayAddress != 0)
			{
				OwnTeam = read<int>(Controller + 0xb18);

				// 读取POV数据 - 按照参考代码的方式
				if (PlayerCameraManager != 0) {
					// POV = 驱动->read<MinimalViewInfo>(驱动->读取指针(PlayerCameraManager + 0x158) + 0x154);
					uintptr_t povPointer = read<uintptr_t>(PlayerCameraManager + 0x158);
					if (povPointer != 0) {
						POV = read<MinimalViewInfo>(povPointer + 0x154);

						// POV.FOV = 驱动->read<float>(PlayerCameraManager + 0x10D0 + 0x10 + 0x18);
						POV.FOV = read<float>(PlayerCameraManager + 0x10D0 + 0x10 + 0x18);

						LOGI("PlayerCameraManager地址: 0x%lx", PlayerCameraManager);
						LOGI("POV指针地址: 0x%lx", povPointer);
						LOGI("POV位置: X=%.2f, Y=%.2f, Z=%.2f", POV.Location.x, POV.Location.y, POV.Location.z);
						LOGI("POV旋转: Pitch=%.2f, Yaw=%.2f, Roll=%.2f", POV.Rotation.Pitch, POV.Rotation.Yaw, POV.Rotation.Roll);
						LOGI("POV视野角度: FOV=%.2f", POV.FOV);
					} else {
						LOGI("错误: POV指针为空");
					}

					// 检查POV数据是否合理
					if (POV.FOV > 0 && POV.FOV < 180) {
						LOGI("POV数据看起来正常，FOV值合理");
					} else {
						LOGI("警告: POV数据可能异常，FOV=%.2f", POV.FOV);
					}
				} else {
					LOGI("错误: CameraAddress为空");
				}

				// read(Matrix2, &SelfCoordinate, sizeof(SelfCoordinate));
				uintptr_t RootPoint = read<uintptr_t>(SelfAddress + 0x278);
				if (read<int>(RootPoint) != 0)
				{
					read(RootPoint + 0x200, &SelfCoordinate, sizeof(SelfCoordinate));
				}
				read(Controller + 0x5a8, &touch_information.MouseCoordinate, sizeof(touch_information.MouseCoordinate)); // Rotator ControlRotation  向右
				自身武器 = read<int>(read<uintptr_t>(SelfAddress + 0xfe8) + 0xbd0);
				自身动作 = read<int>(read<uintptr_t>(SelfAddress + 0x1538));
				if (mode == 1)
				{
					read(CameraAddress + 0x600, &SelfViewCoordinate, sizeof(SelfViewCoordinate)); // 向左
					IsFire = read<int>(SelfAddress + 0x23e0);
					bIsGunADS = read<int>(SelfAddress + 0x1668);
					isSpecialWeaponFire = read<int>(read<uintptr_t>(read<uintptr_t>(SelfAddress + 0xfe8) + 0x19c0) + 0x234);
					Fov = read<float>(read<uintptr_t>(read<uintptr_t>(SelfAddress + 0x5588) + 0x608) + 0x630);
					bulletSpeed = read<float>(read<uintptr_t>(read<uintptr_t>(SelfAddress + 0x33f0 + 0x20) + 0x19d0) + 0x13f4);
					YuCurrentVehicle = (read<uintptr_t>(SelfAddress + 0x1160));
				}
			}
			OBJECTCOUNT[0] = 0;
			OBJECTCOUNT[1] = 0;
			int 人机数量 = 0;
			int 真人数量 = 0;
			smokeCount = 0;			  // 重置烟雾计数
			smokeCoordinates.clear(); // 清空烟雾坐标数组
			for (int count = 0; count < ArraysCount; count++)
			{
				uintptr_t ObjectAddress = read<uintptr_t>(ArrayAddress + 0x8 * count);
				read(ObjectAddress + 0xfdc, &Shared_Information[OBJECTCOUNT[0]].VelocitySafety, 12);
				if (ObjectAddress == 0 || ArrayAddress == ObjectAddress)
				{
					continue;
				}
				ItemName = GetClassName(ObjectAddress, 0x18); // 读取类名
				if (strlen(ItemName.c_str()) < 6 || strlen(ItemName.c_str()) >= 45)
				{
					continue;
				}
				uintptr_t RootPoint = read<uintptr_t>(ObjectAddress + 0x278); // 对方坐标
				if (read<int>(RootPoint) != 0)
				{
					read(RootPoint + 0x200, &ObjectCoordinate, sizeof(ObjectCoordinate));
				}
				if (ObjectCoordinate.x == 0 || ObjectCoordinate.y == 0 || ObjectCoordinate.z == 0)
				{
					continue;
				}
				// 临时保留Matrix读取用于对比调试
				uintptr_t MatrixAddress = GetPointer(ModulesBase[0], 0x12132D60, 0x20, 0x270);
				if (read(MatrixAddress, &Matrix[0][0], 64))
				{
					touch_information.Scal = sqrt(Matrix[0][0] * Matrix[0][0] + Matrix[1][0] * Matrix[1][0] + Matrix[2][0] * Matrix[2][0]);
					LOGI("Matrix读取成功，缩放值: %.2f", touch_information.Scal);
					LOGI("Matrix[0]: %.2f, %.2f, %.2f, %.2f", Matrix[0][0], Matrix[0][1], Matrix[0][2], Matrix[0][3]);
				} else {
					LOGI("Matrix读取失败，使用POV方法计算缩放");
					// 使用POV方法时，可以用距离来计算缩放
					VecTor3 deltaPos = ObjectCoordinate - POV.Location;
					touch_information.Scal = sqrt(deltaPos.x * deltaPos.x + deltaPos.y * deltaPos.y + deltaPos.z * deltaPos.z) / 100.0f;
				}
				if (read<float>(ObjectAddress + 0x3518) == 479.5) // float HighWalkSpeed;
				{
					if (read<int>(ObjectAddress + 0xf58) & 1) // bool bDead;
					{
						continue;
					}
					GetDistance(ObjectCoordinate, SelfCoordinate, Shared_Information[OBJECTCOUNT[0]].Distance);
					Shared_Information[OBJECTCOUNT[0]].Team = read<int>(ObjectAddress + 0xac0); // 对象队伍
					if (Shared_Information[OBJECTCOUNT[0]].Team == OwnTeam)
					{
						continue;
					} // 队伍等于自己队伍或队伍小于1不绘制
					Shared_Information[OBJECTCOUNT[0]].Health = read<float>(ObjectAddress + 0xed8) * 100 / read<float>(ObjectAddress + 0xee0); // 当前血量
					if (Shared_Information[OBJECTCOUNT[0]].Health > 100)
					{
						continue;
					} // 血量大于100不绘制
					Shared_Information[OBJECTCOUNT[0]].IsBoot = read<int>(ObjectAddress + 0xadc); // 人机判断
					if (imguiswitch_information.boolswitch[9] && Shared_Information[OBJECTCOUNT[0]].IsBoot == 1)
					{
						continue;
					}
					if (Shared_Information[OBJECTCOUNT[0]].IsBoot == 1)
					{
						人机数量++;
						Shared_Information[OBJECTCOUNT[0]].ColorChanGeable = imguiswitch_information.colorswitch[6]; // 人机颜色
						BoneColor = imguiswitch_information.colorswitch[6];											 // 人机颜色
						BoxColor = imguiswitch_information.colorswitch[6];											 // 人机颜色
						LineColor = imguiswitch_information.colorswitch[6];
						sprintf(Shared_Information[OBJECTCOUNT[0]].PlayerName, "人机%d", OBJECTCOUNT[0]);
					}
					else if (Shared_Information[OBJECTCOUNT[0]].IsBoot == 0)
					{
						真人数量++;
						BoneColor = imguiswitch_information.colorswitch[1];
						BoxColor = imguiswitch_information.colorswitch[0];
						LineColor = imguiswitch_information.colorswitch[2];
						Shared_Information[OBJECTCOUNT[0]].ColorChanGeable = imguiswitch_information.colorswitch[5];	// 真人颜色
						GetUTF8(Shared_Information[OBJECTCOUNT[0]].PlayerName, read<uintptr_t>(ObjectAddress + 0xa40)); // 获取真人名字
					}
					else
					{
						人机数量++;
						Shared_Information[OBJECTCOUNT[0]].ColorChanGeable = imguiswitch_information.colorswitch[6]; // 人机颜色
						BoneColor = imguiswitch_information.colorswitch[6];											 // 人机颜色
						BoxColor = imguiswitch_information.colorswitch[6];											 // 人机颜色
						LineColor = imguiswitch_information.colorswitch[6];
						sprintf(Shared_Information[OBJECTCOUNT[0]].PlayerName, "高级人机%d", OBJECTCOUNT[0]);
					}
					// 骨骼一列的东西

					MeshAddress = read<uintptr_t>(ObjectAddress + 0x600);
					if (read<int>(MeshAddress) != 0)
					{
						ReadBone(MeshAddress + 0x1F0, MeshTrans);
						TransformTurnMatrix(XMatrix, MeshTrans);
					}
					BoneAddress = read<uintptr_t>(MeshAddress + 0x7F8) + 0x30;																									// BodySetup* ProcMeshBodySetup;
					GetBoneTransform(5, Shared_Information[OBJECTCOUNT[0]].WorldSkeletonCoordinates[0], Shared_Information[OBJECTCOUNT[0]].ScreenSkeletonCoordinates[0]);		// 头部骨骼点
					GetBoneTransform(AIMBONE, Shared_Information[OBJECTCOUNT[0]].WorldSkeletonCoordinates[1], Shared_Information[OBJECTCOUNT[0]].ScreenSkeletonCoordinates[1]); // 胸部骨骼点

					int skeletonType = read<int>(MeshAddress + 0x7F8 + 0x8);
					switch (skeletonType)
					{
					case 68:
						skeletonList = SkeletonList_New;
						if (skeletonList != nullptr)
						{
							GetBoneTransform(57, Shared_Information[OBJECTCOUNT[0]].WorldSkeletonCoordinates[2], Shared_Information[OBJECTCOUNT[0]].ScreenSkeletonCoordinates[2]);
							GetBoneTransform(61, Shared_Information[OBJECTCOUNT[0]].WorldSkeletonCoordinates[3], Shared_Information[OBJECTCOUNT[0]].ScreenSkeletonCoordinates[3]);
						}
						break;
					case 70:
						skeletonList = SkeletonList_S30;
						if (skeletonList != nullptr)
						{
							GetBoneTransform(57, Shared_Information[OBJECTCOUNT[0]].WorldSkeletonCoordinates[2], Shared_Information[OBJECTCOUNT[0]].ScreenSkeletonCoordinates[2]);
							GetBoneTransform(61, Shared_Information[OBJECTCOUNT[0]].WorldSkeletonCoordinates[3], Shared_Information[OBJECTCOUNT[0]].ScreenSkeletonCoordinates[3]);
						}
						break;
					case 72:
						skeletonList = SkeletonList_Yan;
						if (skeletonList != nullptr)
						{
							GetBoneTransform(59, Shared_Information[OBJECTCOUNT[0]].WorldSkeletonCoordinates[2], Shared_Information[OBJECTCOUNT[0]].ScreenSkeletonCoordinates[2]);
							GetBoneTransform(63, Shared_Information[OBJECTCOUNT[0]].WorldSkeletonCoordinates[3], Shared_Information[OBJECTCOUNT[0]].ScreenSkeletonCoordinates[3]);
						}
						break;
					default:
						skeletonList = SkeletonList_Old;
						if (skeletonList != nullptr)
						{
							GetBoneTransform(54, Shared_Information[OBJECTCOUNT[0]].WorldSkeletonCoordinates[2], Shared_Information[OBJECTCOUNT[0]].ScreenSkeletonCoordinates[2]);
							GetBoneTransform(58, Shared_Information[OBJECTCOUNT[0]].WorldSkeletonCoordinates[3], Shared_Information[OBJECTCOUNT[0]].ScreenSkeletonCoordinates[3]);
						}
						break;
					}

					LeftFootSkeleton = Shared_Information[OBJECTCOUNT[0]].WorldSkeletonCoordinates[1];
					LeftFootSkeleton.z -= 20;
					this->WorldToScreenPOV(LeftFoot_Skeleton, LeftFootSkeleton);

					RihtFootSkeleton = Shared_Information[OBJECTCOUNT[0]].WorldSkeletonCoordinates[2];
					RihtFootSkeleton.z -= 20;
					this->WorldToScreenPOV(RihtFoot_Skeleton, RihtFootSkeleton);

					HeadSkeleton = Shared_Information[OBJECTCOUNT[0]].WorldSkeletonCoordinates[0];
					HeadSkeleton.z += 20;
					this->WorldToScreenPOV(Shared_Information[OBJECTCOUNT[0]].HeadSkeletonCoordinates, HeadSkeleton);

					Shared_Information[OBJECTCOUNT[0]].BottomLeftFootSkeleton.x = Shared_Information[OBJECTCOUNT[0]].ScreenCoordinates.x - Shared_Information[OBJECTCOUNT[0]].ScreenCoordinates.z / 2;
					Shared_Information[OBJECTCOUNT[0]].BottomLeftFootSkeleton.y = Shared_Information[OBJECTCOUNT[0]].HeadSkeletonCoordinates;
					Shared_Information[OBJECTCOUNT[0]].BottomRihtFootSkeleton.x = Shared_Information[OBJECTCOUNT[0]].ScreenCoordinates.x + Shared_Information[OBJECTCOUNT[0]].ScreenCoordinates.z / 2;
					Shared_Information[OBJECTCOUNT[0]].BottomRihtFootSkeleton.y = max(LeftFoot_Skeleton, RihtFoot_Skeleton);
					if (this->WorldToScreenPOV(Shared_Information[OBJECTCOUNT[0]].ScreenCoordinates, Shared_Information[OBJECTCOUNT[0]].ScreenCamera, ObjectCoordinate))
					{

						if (imguiswitch_information.boolswitch[50])
						{
							float enemyAngle = read<float>(ObjectAddress + 0x1a8);
							VecTor2 enemyToSelfAngles = FastAtan2(SelfCoordinate, ObjectCoordinate);
							enemyAngle = fmod(enemyAngle + 180.0f, 360.0f) - 180.0f;
							float targetYaw = fmod(enemyToSelfAngles.y + 180.0f, 360.0f) - 180.0f;
							float angleDiff = abs(targetYaw - enemyAngle);
							if (angleDiff > 180.0f)
							{
								angleDiff = 360.0f - angleDiff;
							}
							if (angleDiff < 40.0f)
							{
								BoneColor = imguiswitch_information.colorswitch[7];
							}
						}
						if (imguiswitch_information.boolswitch[0] && Shared_Information[OBJECTCOUNT[0]].Team != -1)
						{
							ImColor currentBoneColor = BoneColor;
							if (!aimidage && imguiswitch_information.boolswitch[24] && OBJECTCOUNT[0] == aimitar)
							{
								currentBoneColor = imguiswitch_information.colorswitch[7];
							}
							for (int i = 0; i < 14; i++)
							{
								GetBoneTransform(skeletonList[i][0], Shared_Information[OBJECTCOUNT[0]].ScreenSkeletonSta[i]);
								GetBoneTransform(skeletonList[i][1], Shared_Information[OBJECTCOUNT[0]].ScreenSkeletonEnd[i]);

								skeleton(drawlist,
										 {Shared_Information[OBJECTCOUNT[0]].ScreenSkeletonSta[i].x, Shared_Information[OBJECTCOUNT[0]].ScreenSkeletonSta[i].y},
										 {Shared_Information[OBJECTCOUNT[0]].ScreenSkeletonEnd[i].x, Shared_Information[OBJECTCOUNT[0]].ScreenSkeletonEnd[i].y},
										 currentBoneColor,
										 imguiswitch_information.floatswitch[0]);
							}
						}
						if (imguiswitch_information.boolswitch[1])
						{ // 方框绘制
							DrawPlayerBox(ImGui::GetForegroundDrawList(), Shared_Information[OBJECTCOUNT[0]].BottomLeftFootSkeleton.x, Shared_Information[OBJECTCOUNT[0]].BottomRihtFootSkeleton.x, Shared_Information[OBJECTCOUNT[0]].BottomRihtFootSkeleton.y, Shared_Information[OBJECTCOUNT[0]].HeadSkeletonCoordinates, Shared_Information[OBJECTCOUNT[0]].ScreenCoordinates.x, Shared_Information[OBJECTCOUNT[0]].ScreenCoordinates.y, BoxColor, imguiswitch_information.floatswitch[0]);
						}

						if (imguiswitch_information.boolswitch[2])
						{
							// 射线绘制
							ImVec2 start = {resolution_information.Width, 0};
							ImVec2 end = {Shared_Information[OBJECTCOUNT[0]].ScreenCoordinates.x, Shared_Information[OBJECTCOUNT[0]].HeadSkeletonCoordinates - 100};
							float thickness = imguiswitch_information.floatswitch[0];
							ImU32 lineColor = ImGui::GetColorU32(LineColor);
							drawlist->AddLine(start, end, lineColor, thickness);
						}
						if (imguiswitch_information.boolswitch[3])
						{ // 血条绘制
							HealthBarType healthBarType;
							switch (imguiswitch_information.intswitch[0])
							{
							case 0:
								healthBarType = CircleArc;
								break;
							case 1:
								healthBarType = RectangleFilled;
								break;
							case 2:
								healthBarType = CustomRectangle;
							}
							DrawHealthBar(drawlist, Shared_Information, healthBarType, imguiswitch_information, OBJECTCOUNT[0]);
						}

						if (imguiswitch_information.boolswitch[4] && 自身武器 == 0) // 名字绘制
						{
							string Text = Shared_Information[OBJECTCOUNT[0]].PlayerName;
							ImVec2 TextSize = ImGui::GetFont()->CalcTextSizeA(18, FLT_MAX, -1, Text.c_str());
							float centerX = Shared_Information[OBJECTCOUNT[0]].ScreenCoordinates.x;
							float topY = Shared_Information[OBJECTCOUNT[0]].HeadSkeletonCoordinates - 10;
							drawlist->AddText(ImGui::GetFont(), 18, {centerX - TextSize.x / 2, topY - TextSize.y}, imguiswitch_information.colorswitch[4], Text.c_str());
						}

						if (imguiswitch_information.boolswitch[5]) // 距离绘制
						{
							// 绘制队伍
							string TeamText = to_string((int)Shared_Information[OBJECTCOUNT[0]].Team);
							// 使用CalcTextSizeA获取更精确的文本尺寸
							ImVec2 TeamTextSize = ImGui::GetFont()->CalcTextSizeA(22, FLT_MAX, -1, TeamText.c_str());
							float centerX = Shared_Information[OBJECTCOUNT[0]].ScreenCoordinates.x;
							float teamY = Shared_Information[OBJECTCOUNT[0]].HeadSkeletonCoordinates - 70;
							// 精确居中绘制队伍
							drawlist->AddText(ImGui::GetFont(), 22, {centerX - TeamTextSize.x / 2, teamY - TeamTextSize.y / 2}, imguiswitch_information.colorswitch[5], TeamText.c_str());

							// 绘制距离
							string DistanceText = to_string((int)Shared_Information[OBJECTCOUNT[0]].Distance);
							// 使用CalcTextSizeA获取更精确的文本尺寸
							ImVec2 DistanceTextSize = ImGui::GetFont()->CalcTextSizeA(20, FLT_MAX, -1, DistanceText.c_str());
							float distanceY = Shared_Information[OBJECTCOUNT[0]].HeadSkeletonCoordinates - 45;
							// 精确居中绘制距离
							drawlist->AddText(ImGui::GetFont(), 20, {centerX - DistanceTextSize.x / 2, distanceY - DistanceTextSize.y / 2}, imguiswitch_information.colorswitch[3], DistanceText.c_str());

							// 检查动作是否在 恰雷动作范围内
							if ((自身武器 == 602004 || 自身武器 == 9825004)) // 判断是否为手雷
							{
								// 精确判断5位数状态码表示投掷中
								if (自身动作 >= 10000 && 自身动作 <= 99999)
								{
									if (Shared_Information[OBJECTCOUNT[0]].Distance < 130)
									{
										if (!isHoldingGrenade)
										{
											initialThrowTime = ImGui::GetTime();
											isHoldingGrenade = true;
										}
										float currentTime = ImGui::GetTime();
										// 优化爆炸时间计算，考虑手雷飞行速度
										float flyTime = Shared_Information[OBJECTCOUNT[0]].Distance / 20.0f; // 调整手雷飞行速度因子
										float instantExplosionTime = 7.0f - (currentTime - initialThrowTime) - flyTime;
										if (instantExplosionTime > 0)
										{
											std::string displayText = std::to_string(static_cast<int>(instantExplosionTime));
											ImVec2 TimeTextSize = ImGui::GetFont()->CalcTextSizeA(50, FLT_MAX, -1, displayText.c_str());
											float centeredX = Shared_Information[OBJECTCOUNT[0]].ScreenCoordinates.x - (TimeTextSize.x / 2);
											绘制加粗文本(50, centeredX, Shared_Information[OBJECTCOUNT[0]].HeadSkeletonCoordinates - 150 - (TimeTextSize.y / 2), ImColor(255, 0, 0, 255), ImColor(ImVec4(0 / 255.f, 0 / 255.f, 0 / 255.f, 0.7f)), displayText.c_str());
										}
									}
								}
								else if (isHoldingGrenade) // 投掷结束，重置状态
								{
									isHoldingGrenade = false;
									initialThrowTime = 0;
								}
							}
						}
						if (imguiswitch_information.boolswitch[6] &&
							(scid = read<int>(read<uintptr_t>(ObjectAddress + 0xfe8) + 0xbd0)) > 0 &&
							获取枪械信息(scid, &图片))
						{
							drawlist->AddImage(图片, ImVec2(Shared_Information[OBJECTCOUNT[0]].ScreenCoordinates.x - 55, Shared_Information[OBJECTCOUNT[0]].HeadSkeletonCoordinates - 125), ImVec2(Shared_Information[OBJECTCOUNT[0]].ScreenCoordinates.x + 59, Shared_Information[OBJECTCOUNT[0]].HeadSkeletonCoordinates - 90));
						}

						if (initializeaimi && imguiswitch_information.boolswitch[26] && aiminur == OBJECTCOUNT[0])
						{
							drawlist->AddLine({resolution_information.Width, resolution_information.Heiht}, {Shared_Information[OBJECTCOUNT[0]].ScreenSkeletonCoordinates[1].x, Shared_Information[OBJECTCOUNT[0]].ScreenSkeletonCoordinates[1].y}, ImColor(255, 255, 255, 255), 2);
						}
					}
					if (imguiswitch_information.boolswitch[7] && Shared_Information[OBJECTCOUNT[0]].IsBoot == 0 && Shared_Information[OBJECTCOUNT[0]].Health > 0)
					{ // 敌背绘制
						VecTor2 tempScreen;
						this->WorldToScreenPOV(tempScreen, ObjectCoordinate);
						Shared_Information[OBJECTCOUNT[0]].ScreenCoordinateshou = tempScreen;
						OffScreen(
							VecTor2(Shared_Information[OBJECTCOUNT[0]].ScreenCoordinateshou.x,
									Shared_Information[OBJECTCOUNT[0]].ScreenCoordinateshou.y),
							Shared_Information[OBJECTCOUNT[0]].ScreenCamera,
							ImColor(arr[Shared_Information[OBJECTCOUNT[0]].Team % length]),
							imguiswitch_information.floatswitch[10] + dx + Shared_Information[OBJECTCOUNT[0]].Distance * 0.3);
					}

					if (imguiswitch_information.boolswitch[8])
					{ // 雷达绘制
						GetRadarCoordinates(Shared_Information[OBJECTCOUNT[0]].RadarCoordinates, touch_information.MouseCoordinate.y, ObjectCoordinate, SelfCoordinate, imguiswitch_information.floatswitch[3]);

						if (!透明)
						{
							drawlist->AddCircleFilled({resolution_information.Width + imguiswitch_information.floatswitch[1], resolution_information.Heiht + imguiswitch_information.floatswitch[2]}, 5, ImColor(255, 255, 255, 255));
						}
						if (CapTivity(imguiswitch_information.floatswitch[4], Shared_Information[OBJECTCOUNT[0]].RadarCoordinates))
						{

							drawlist->AddCircleFilled({resolution_information.Width + imguiswitch_information.floatswitch[1] + Shared_Information[OBJECTCOUNT[0]].RadarCoordinates.x, resolution_information.Heiht + imguiswitch_information.floatswitch[2] + Shared_Information[OBJECTCOUNT[0]].RadarCoordinates.y}, 5, Shared_Information[OBJECTCOUNT[0]].ColorChanGeable);
						}
					}
					OBJECTCOUNT[0]++;
				}

				if (!ItemName.empty() && ItemName.find("PlayerPawn") == std::string::npos)
				{
					GetDistance(ObjectCoordinate, SelfCoordinate, Throw_Information[OBJECTCOUNT[1]].Distance);
					std::string matchedName = "";
					VecTor4 tempThrowScreen;
					float tempThrowCamera;
					if (this->WorldToScreenPOV(tempThrowScreen, tempThrowCamera, ObjectCoordinate))
					{
						Throw_Information[OBJECTCOUNT[1]].ScreenCoordinates = tempThrowScreen;
						Throw_Information[OBJECTCOUNT[1]].ScreenCamera = tempThrowCamera;
						if (imguiswitch_information.boolswitch[23] && ItemName.find("ProjSmoke_BP_C") != std::string::npos)
						{
							smokeCoordinates.push_back(ObjectCoordinate);
							smokeCount++; // 增加烟雾计数
						}
						if (imguiswitch_information.boolswitch[10])
						{
							std::string debugText = ItemName + "_" + std::to_string(static_cast<int>(Throw_Information[OBJECTCOUNT[1]].Distance)) + "米";
							// 使用CalcTextSizeA获取更精确的文本尺寸
							ImVec2 debugTextSize = ImGui::GetFont()->CalcTextSizeA(23, FLT_MAX, -1, debugText.c_str());
							float centerX = Throw_Information[OBJECTCOUNT[1]].ScreenCoordinates.x;
							绘制加粗文本(23, centerX - debugTextSize.x / 2, Throw_Information[OBJECTCOUNT[1]].ScreenCoordinates.y, ImColor(ImVec4(1.0f, 1.0f, 1.0f, 1.0f)), ImColor(ImVec4(0.0f, 0.0f, 0.0f, 0.7f)), debugText.c_str());
							if (Throw_Information[OBJECTCOUNT[1]].Distance <= 100)
							{
								std::ofstream outFile("Debug.txt", std::ios::app);
								if (outFile.is_open())
								{
									outFile << "类名: " << ItemName << ", 距离: " << Throw_Information[OBJECTCOUNT[1]].Distance << "m" << std::endl;
									outFile.close();
								}
								else
								{
									std::cerr << "Unable to open file for writing." << std::endl;
								}
							}
						}
						else
						{
							struct ItemConfig
							{
								std::string name;  // 物资名称
								ImColor color;	   // 物资颜色
								float minDistance; // 最小距离限制
								float maxDistance; // 最大距离限制
								bool valid;		   // 是否满足条件
							};
							// 配置物资类型及其属性
							std::vector<ItemConfig> itemConfigs = {
								{"投掷", imguiswitch_information.colorswitch[8], 0, 100, imguiswitch_information.boolswitch[11] && isPartialMatchedType(1, ItemName, matchedName)},
								{"载具", imguiswitch_information.colorswitch[9], 10, 600, imguiswitch_information.boolswitch[12] && 自身武器 == 0 && isPartialMatchedType(2, ItemName, matchedName)},
								{"防具", imguiswitch_information.colorswitch[10], 0, 100, imguiswitch_information.boolswitch[13] && 自身武器 == 0 && isPartialMatchedType(3, ItemName, matchedName)},
								{"道具", imguiswitch_information.colorswitch[11], 0, 1700, imguiswitch_information.boolswitch[14] && 自身武器 == 0 && isPartialMatchedType(4, ItemName, matchedName)},
								{"盒子", imguiswitch_information.colorswitch[12], 0, 100, imguiswitch_information.boolswitch[15] && 自身武器 == 0 && isPartialMatchedType(5, ItemName, matchedName)},
								{"药品", imguiswitch_information.colorswitch[13], 0, 100, imguiswitch_information.boolswitch[16] && 自身武器 == 0 && isPartialMatchedType(6, ItemName, matchedName)},
								{"子弹", imguiswitch_information.colorswitch[14], 0, 100, imguiswitch_information.boolswitch[17] && 自身武器 == 0 && isPartialMatchedType(7, ItemName, matchedName)},
								{"762", imguiswitch_information.colorswitch[15], 0, 100, imguiswitch_information.boolswitch[18] && 自身武器 == 0 && isPartialMatchedType(8, ItemName, matchedName)},
								{"556", imguiswitch_information.colorswitch[16], 0, 100, imguiswitch_information.boolswitch[30] && 自身武器 == 0 && isPartialMatchedType(9, ItemName, matchedName)},
								{"冲锋", imguiswitch_information.colorswitch[17], 0, 100, imguiswitch_information.boolswitch[31] && 自身武器 == 0 && isPartialMatchedType(10, ItemName, matchedName)},
								{"霰弹", imguiswitch_information.colorswitch[18], 0, 100, imguiswitch_information.boolswitch[32] && 自身武器 == 0 && isPartialMatchedType(11, ItemName, matchedName)},
								{"狙击", imguiswitch_information.colorswitch[19], 0, 100, imguiswitch_information.boolswitch[33] && 自身武器 == 0 && isPartialMatchedType(12, ItemName, matchedName)},
								{"其他", imguiswitch_information.colorswitch[20], 0, 100, imguiswitch_information.boolswitch[34] && 自身武器 == 0 && isPartialMatchedType(13, ItemName, matchedName)},
								{"步配", imguiswitch_information.colorswitch[21], 0, 100, imguiswitch_information.boolswitch[35] && 自身武器 == 0 && isPartialMatchedType(14, ItemName, matchedName)},
								{"倍镜", imguiswitch_information.colorswitch[22], 0, 100, imguiswitch_information.boolswitch[36] && 自身武器 == 0 && isPartialMatchedType(15, ItemName, matchedName)},
								{"地铁", imguiswitch_information.colorswitch[23], 0, 600, imguiswitch_information.boolswitch[19] && 自身武器 == 0 && isPartialMatchedType(16, ItemName, matchedName) && read<int>(ObjectAddress + 0x270) == 0}};
							// 遍历所有物资类型并处理
							for (const auto &config : itemConfigs)
							{
								// 检查是否有效并在距离限制内
								if (config.valid && Throw_Information[OBJECTCOUNT[1]].Distance >= config.minDistance && Throw_Information[OBJECTCOUNT[1]].Distance <= config.maxDistance)
								{
									std::string displayText = matchedName + " " + std::to_string(static_cast<int>(Throw_Information[OBJECTCOUNT[1]].Distance)) + "M";

									if (config.name == "载具" && imguiswitch_information.boolswitch[38] && 自身武器 == 0)
									{
										uintptr_t VehicleAddress = read<uintptr_t>(ObjectAddress + 0xac8);
										if (read<int>(VehicleAddress) != 0)
										{
											float vehicleFuel = read<float>(VehicleAddress + 0x214) * 100 / read<float>(VehicleAddress + 0x210);
											displayText += " " + std::to_string(static_cast<int>(vehicleFuel)) + "%";
										}
									}
									if (ItemName.find("ojGrenade_BP_C") != std::string::npos)
									{
										float ExplosionTime = read<float>(ObjectAddress + 0x81c);
										float ReplicatedWorldTimeSeconds = read<float>(read<long>(read<long>(ModulesBase[0] + 0x12161358) + 0x9b0) + 0x5a4);
										float temptime = ExplosionTime - ReplicatedWorldTimeSeconds;
										if (temptime <= 0)
											continue;
										ImTextureID 手雷图片 = nullptr;
										if (获取枪械信息(602004, &手雷图片))
										{
											ImU32 red_tint = IM_COL32(255, 0, 0, 255);
											ImVec2 uv0(0, 0), uv1(1, 1);
											float centerX = Throw_Information[OBJECTCOUNT[1]].ScreenCoordinates.x;
											float centerY = Throw_Information[OBJECTCOUNT[1]].ScreenCoordinates.y;
											drawlist->AddImage(手雷图片, ImVec2(centerX - 50, centerY - 32), ImVec2(centerX + 60, centerY + 25), uv0, uv1, red_tint);
											ExplosionRange(ObjectCoordinate, ImColor(255, 0, 0, 60), 150, 2.5f);
											std::stringstream ss;
											ss << static_cast<int>(temptime);
											std::string timeText = ss.str();
											ImVec2 timeTextSize = ImGui::GetFont()->CalcTextSizeA(imguiswitch_information.intswitch[6], FLT_MAX, -1, timeText.c_str());
											绘制加粗文本(imguiswitch_information.intswitch[6], centerX - timeTextSize.x / 2, centerY + 25, imguiswitch_information.colorswitch[8], ImColor(ImVec4(0.0f, 0.0f, 0.0f, 0.7f)), timeText.c_str());
										}
									}
									else
									{
										ImVec2 itemTextSize = ImGui::GetFont()->CalcTextSizeA(imguiswitch_information.intswitch[6], FLT_MAX, -1, displayText.c_str());
										float centerX = Throw_Information[OBJECTCOUNT[1]].ScreenCoordinates.x;
										绘制加粗文本(imguiswitch_information.intswitch[6], centerX - itemTextSize.x / 2, Throw_Information[OBJECTCOUNT[1]].ScreenCoordinates.y, config.color, ImColor(ImVec4(0.0f, 0.0f, 0.0f, 0.7f)), displayText.c_str());
									}
									OBJECTCOUNT[1]++;
									break; // 匹配到一个物资后立即退出循环
								}
							}
						}
					}
				}
			}
			if (imguiswitch_information.boolswitch[20] && YuCurrentVehicle == 0)
			{
				// 初始化一些变量
				bool isShotgun = false;			   // 是否是霰弹枪
				bool isSniper = false;			   // 是否是狙击枪
				float standCrouchPressure = 0.99f; // 站立或蹲下时的压力
				float pronePressure = 0.95f;	   // 趴下时的压力

				// 调用函数计算武器压力值
				WeaponPressureGunValue(自身武器, Fov, isShotgun, isSniper, standCrouchPressure, pronePressure);

				// 判断是否需要关闭瞄准
				if ((!imguiswitch_information.boolswitch[21] && isShotgun || (imguiswitch_information.boolswitch[22] && isSniper)))
				{
					// 关闭瞄准相关的开关和变量
					aimiswitch = false;
					touchdriven.setAimIo(false);
					aimidage = false;
					memset(&aimiant, 0, sizeof(aimiant));
					aimitar = -1;
					aimidistance = 0;
				}
				else
				{
					if (!aimiswitch)
					{
						// 初始化瞄准相关的变量
						aimi_abs = 0;
						aimitar = -1;
						for (int i = 0; i < OBJECTCOUNT[0]; i++)
						{
							// 判断目标是否有效
							aimifal = (imguiswitch_information.intswitch[2] == 1 && Shared_Information[i].Health <= 0);
							aimibot = (imguiswitch_information.intswitch[4] == 1 && Shared_Information[i].IsBoot == 1);
							aimi_x_abs = abs(resolution_information.Width - Shared_Information[i].ScreenSkeletonCoordinates[1].x);
							aimi_y_abs = abs(resolution_information.Heiht - Shared_Information[i].ScreenSkeletonCoordinates[1].y);
							// 判断目标是否在屏幕上并且在有效距离内
							if (Shared_Information[i].ScreenCamera > 0.01 && (Shared_Information[i].Distance <= imguiswitch_information.floatswitch[6] || Shared_Information[i].Distance <= imguiswitch_information.floatswitch[56]) && imguiswitch_information.boolswitch[20] && !aimifal && !aimibot && !aimidage && aimi_x_abs < imguiswitch_information.floatswitch[5] && aimi_y_abs < imguiswitch_information.floatswitch[5])
							{
								switch (imguiswitch_information.intswitch[3])
								{
								case 0:
									// 选择最近的目标
									if (aimi_abs == 0)
									{
										aimitar = i;
										aimi_abs = aimi_x_abs + aimi_y_abs;
									}
									else
									{
										if (aimi_abs > aimi_x_abs + aimi_y_abs)
										{
											aimitar = i;
											aimi_abs = aimi_x_abs + aimi_y_abs;
										}
									}
									break;
								case 1:
									// 选择距离最近的目标
									if (aimi_abs == 0)
									{
										aimitar = i;
										aimidistance = Shared_Information[i].Distance;
										aimi_abs = aimi_x_abs + aimi_y_abs;
									}
									else
									{
										if (aimidistance > Shared_Information[i].Distance)
										{
											aimitar = i;
											aimidistance = Shared_Information[i].Distance;
											aimi_abs = aimi_x_abs + aimi_y_abs;
										}
									}
									break;
								}
							}
						}
						if (aimitar == -1)
						{
							aimiswitch = false;
						}
						else if (aimitar > -1)
						{
							aimiswitch = true;
							aimiant = Shared_Information[aimitar].WorldSkeletonCoordinates[1];
						}
						aiminur = aimitar;
					}
					// 更新目标状态
					aimifal = (imguiswitch_information.intswitch[2] == 1 && Shared_Information[aimitar].Health <= 0);
					aimibot = (imguiswitch_information.intswitch[4] == 1 && Shared_Information[aimitar].IsBoot == 1);
					aimi_x_abs = abs(resolution_information.Width - Shared_Information[aimitar].ScreenSkeletonCoordinates[1].x);
					aimi_y_abs = abs(resolution_information.Heiht - Shared_Information[aimitar].ScreenSkeletonCoordinates[1].y);
					bool isInSmoke = false;																	// 存储是否在烟雾内的状态
					if (imguiswitch_information.boolswitch[23] && aimitar >= 0 && aimitar < OBJECTCOUNT[0]) // 确保aimitar在有效范围内
					{
						VecTor3 skeletonCoordinate = Shared_Information[aimitar].WorldSkeletonCoordinates[1];
						for (int i = 0; i < smokeCount; i++)
						{
							float distance;
							GetDistance(skeletonCoordinate, smokeCoordinates[i], distance);
							if (distance < 10.0f)
							{
								isInSmoke = true;
								break;
							}
						}
					}

					float maxAllowedDistance = isShotgun ? 20.0f : ((bIsGunADS == 1 || bIsGunADS == 257) ? imguiswitch_information.floatswitch[6] : ((bIsGunADS == 0 || bIsGunADS == 256) ? imguiswitch_information.floatswitch[11] : 0.0f));

					AIMBONE = isShotgun ? BONE_SHOTGUN : ((bIsGunADS == 1 || bIsGunADS == 257) ? BONE_SCOPED : ((bIsGunADS == 0 || bIsGunADS == 256) ? BONE_HIPFIRE : AIMBONE));

					bool canFire = (Shared_Information[aimitar].ScreenCamera > 0.01 &&
									(IsFire == 1 || ((isShotgun || isSniper) && isSpecialWeaponFire)) &&
									Shared_Information[aimitar].Distance <= maxAllowedDistance &&
									// 其他条件
									imguiswitch_information.boolswitch[20] && !aimifal && !aimibot &&
									!touch_information.TouchAimAtControl && aimiswitch &&
									// 坐标条件
									aimi_x_abs < imguiswitch_information.floatswitch[5] &&
									aimi_y_abs < imguiswitch_information.floatswitch[5] &&
									// 武器和健康状态
									(isShotgun || 自身武器 == 104005 ||
									 Shared_Information[aimitar].Health <= imguiswitch_information.floatswitch[8]) &&
									!isInSmoke);
					if (canFire)
					{
						if (Shared_Information[aimitar].Distance <= 40)
						{
							bulletFlyTime = (Shared_Information[aimitar].Distance / bulletSpeed) * imguiswitch_information.floatswitch[12] * 100.0f;
						}
						else
						{
							bulletFlyTime = (Shared_Information[aimitar].Distance / bulletSpeed) * imguiswitch_information.floatswitch[7] * 100.0f;
						}
						aimiant = Shared_Information[aimitar].WorldSkeletonCoordinates[1] += Shared_Information[aimitar].VelocitySafety * bulletFlyTime;
						if ((imguiswitch_information.boolswitch[21] && isShotgun) || (IsFire == 1))
						{
							// 根据自身动作调整视角
							if (自身动作 == 272 || 自身动作 == 1296 || 自身动作 == 1297 || 自身动作 == 273 || 自身动作 == 4368 || 自身动作 == 4369 || 自身动作 == 5392 || 自身动作 == 5393 || 自身动作 == 400 || 自身动作 == 403) // 站着动作
							{
								if (Shared_Information[aimitar].Distance)
								{
									SelfViewCoordinate.z += Shared_Information[aimitar].Distance * imguiswitch_information.floatswitch[20] * standCrouchPressure;
								}
							}
							else if (自身动作 == 288 || 自身动作 == 289 || 自身动作 == 4384 || 自身动作 == 4385 || 自身动作 == 5408 || 自身动作 == 5409 || 自身动作 == 1312 || 自身动作 == 1313) // 蹲着动作
							{
								if (Shared_Information[aimitar].Distance)
								{
									SelfViewCoordinate.z += Shared_Information[aimitar].Distance * imguiswitch_information.floatswitch[21] * standCrouchPressure;
								}
							}
							else if (自身动作 == 320 || 自身动作 == 1344) // 趴着动作
							{
								if (Shared_Information[aimitar].Distance)
								{
									SelfViewCoordinate.z += Shared_Information[aimitar].Distance * imguiswitch_information.floatswitch[22] * pronePressure;
								}
							}
							touchdriven.setFireIo(true);
							touchdriven.setAimIo(true);
						}
						else
						{
							touchdriven.setFireIo(false);
							aimiswitch = false;
							touchdriven.setAimIo(false);
							memset(&aimiant, 0, sizeof(aimiant));
						}
						if (aimiant.x != 0 || aimiant.y != 0 || aimiant.z != 0)
						{
							touch_information.AimingCoordinates = FastAtan2(aimiant, SelfViewCoordinate);
						}
					}
					else
					{
						touchdriven.setFireIo(false);
						aimiswitch = false;
						touchdriven.setAimIo(false);
						memset(&aimiant, 0, sizeof(aimiant));
					}
				}
			}
			float CoordinatesW = resolution_information.ScreenWidth * touch_information.TouchRadius;
			float CoordinatesX = resolution_information.ScreenWidth * touch_information.TouchPoints.y + RandomOffset(offsetRangeX);
			float CoordinatesY = resolution_information.ScreenHeiht * (1 - touch_information.TouchPoints.x) + RandomOffset(offsetRangeY);
			if (initializeaimi && imguiswitch_information.boolswitch[27])
			{
				string Text = "";
				Text += "触摸控制区域";
				ImVec2 TextSize = ImGui::CalcTextSize(Text.c_str());
				drawlist->AddText(NULL, 30, {CoordinatesX - (TextSize.x / 2), CoordinatesY - (TextSize.y / 2)}, ImColor(255, 255, 255, 255), Text.c_str());
				drawlist->AddRectFilled({CoordinatesX - CoordinatesW, CoordinatesY - CoordinatesW}, {CoordinatesX + CoordinatesW, CoordinatesY + CoordinatesW}, ImColor(200, 0, 0, 100));
			}
			if (initializeaimi && imguiswitch_information.boolswitch[20])
			{
				float radius = 0.0f;
				if (imguiswitch_information.intswitch[5] == 0) // 固定范围
				{
					radius = imguiswitch_information.floatswitch[5];
				}
				else if (imguiswitch_information.intswitch[5] == 1) // 动态范围
				{
					// 配置参数
					const float minRange = imguiswitch_information.floatswitch[9];
					const float maxRange = imguiswitch_information.floatswitch[10];
					const float smoothFactor = 0.2f;			   // 平滑过渡系数
					const float PX = resolution_information.Width; // 屏幕中心X坐标
					const float PY = resolution_information.Heiht; // 屏幕中心Y坐标
					// 默认使用最大范围
					radius = maxRange;
					if (aimitar != -1)
					{
						// 已锁定目标，直接使用与目标的欧几里得距离
						float targetX = Shared_Information[aimitar].ScreenSkeletonCoordinates[1].x;
						float targetY = Shared_Information[aimitar].ScreenSkeletonCoordinates[1].y;
						radius = sqrt(pow(PX - targetX, 2) + pow(PY - targetY, 2));
					}
					else
					{
						// 寻找离准心最近的目标
						float closestDistance = FLT_MAX;
						for (int i = 0; i < OBJECTCOUNT[0]; i++)
						{
							if (Shared_Information[i].ScreenCamera > 0.01f)
							{ // 确保目标在屏幕上
								float targetX = Shared_Information[i].ScreenSkeletonCoordinates[1].x;
								float targetY = Shared_Information[i].ScreenSkeletonCoordinates[1].y;
								float distance = sqrt(pow(PX - targetX, 2) + pow(PY - targetY, 2));

								if (distance < closestDistance)
								{
									closestDistance = distance;
								}
							}
						}
						// 如果找到了目标，使用其距离作为半径
						if (closestDistance != FLT_MAX)
						{
							radius = closestDistance;
						}
					}
					// 应用范围限制
					radius = min(max(radius, minRange), maxRange);
					// 平滑过渡
					float currentRadius = imguiswitch_information.floatswitch[5];
					if (currentRadius > 0)
					{
						radius = currentRadius + (radius - currentRadius) * smoothFactor;
					}
					// 更新范围
					imguiswitch_information.floatswitch[5] = radius;
				}
				else if (imguiswitch_information.intswitch[5] == 2) // 距离自适应范围：距离越近范围越大，距离越远范围越小
				{
					// 配置参数
					const float minRange = imguiswitch_information.floatswitch[9];
					const float maxRange = imguiswitch_information.floatswitch[10];
					const float startDecayDistance = 5.0f;	   // 开始衰减的距离
					const float maxEffectiveDistance = 150.0f; // 最大有效距离
					const float farDistanceThreshold = 100.0f; // 远距离阈值
					const float farDistanceMultiplier = 0.8f;  // 远距离额外缩小系数
					const float smoothFactor = 0.2f;		   // 平滑过渡系数
					// 默认使用最大范围
					radius = maxRange;
					// 获取目标距离（已锁定或最近的目标）
					float targetDistance = FLT_MAX;
					if (aimitar != -1 && aimitar < OBJECTCOUNT[0])
					{
						// 已锁定目标，直接使用其距离
						targetDistance = Shared_Information[aimitar].Distance;
					}
					else
					{
						// 查找最近的目标
						for (int i = 0; i < OBJECTCOUNT[0]; i++)
						{
							if (Shared_Information[i].ScreenCamera > 0.01f && // 确保目标在屏幕上
								Shared_Information[i].Distance < targetDistance)
							{
								targetDistance = Shared_Information[i].Distance;
							}
						}
					}
					// 计算自适应范围
					if (targetDistance != FLT_MAX)
					{
						if (targetDistance <= startDecayDistance)
						{
							// 距离非常近，使用最大范围
							radius = maxRange;
						}
						else
						{
							// 计算距离比例并限制在[0,1]范围
							float distanceRatio = (targetDistance - startDecayDistance) / (maxEffectiveDistance - startDecayDistance);
							distanceRatio = min(1.0f, max(0.0f, distanceRatio));
							// 使用平方曲线创建更陡峭的衰减
							float rangeRatio = pow(1.0f - distanceRatio, 2.0f);
							// 计算基础范围
							radius = minRange + rangeRatio * (maxRange - minRange);
							// 对远距离目标进一步缩小范围
							if (targetDistance > farDistanceThreshold)
							{
								radius = max(minRange, radius * farDistanceMultiplier);
							}
						}
						// 平滑过渡
						float currentRadius = imguiswitch_information.floatswitch[5];
						if (currentRadius > 0)
						{
							radius = currentRadius + (radius - currentRadius) * smoothFactor;
						}
					}
					// 更新范围
					imguiswitch_information.floatswitch[5] = radius;
				}
				// 绘制圆圈
				if (imguiswitch_information.boolswitch[25])
				{
					drawlist->AddCircle({resolution_information.Width, resolution_information.Heiht}, radius, ImColor(255, 255, 255, 255), 0, 2);
				}
			}
			std::string playerText = std::string("P: ") + (真人数量 < 10 ? "0" : "") + std::to_string(真人数量);
			std::string botText = std::string("A: ") + (人机数量 < 10 ? "0" : "") + std::to_string(人机数量);
			auto textSize = ImGui::GetFont()->CalcTextSizeA(60, FLT_MAX, -1, (playerText + "    " + botText).c_str());
			float px = resolution_information.Width;
			float py = resolution_information.Heiht * 0.13f;
			ImVec2 textPos(px - textSize.x / 2, py - textSize.y / 2);
			auto playerTextSize = ImGui::GetFont()->CalcTextSizeA(60, FLT_MAX, -1, playerText.c_str());
			ImVec2 playerTextPos(textPos.x, textPos.y);
			ImVec2 botTextPos(textPos.x + playerTextSize.x + 20.0f, textPos.y);
			ImU32 outlineColor1 = ImColor(0, 0, 0, 255);
			ImU32 playerColor = imguiswitch_information.colorswitch[1];
			ImU32 botColor = imguiswitch_information.colorswitch[6];
			ImGui::GetBackgroundDrawList()->AddText(NULL, 60, ImVec2(playerTextPos.x - 0.8f, playerTextPos.y - 0.8f), outlineColor1, playerText.c_str());
			ImGui::GetBackgroundDrawList()->AddText(NULL, 60, ImVec2(playerTextPos.x + 0.8f, playerTextPos.y + 0.8f), outlineColor1, playerText.c_str());
			ImGui::GetBackgroundDrawList()->AddText(NULL, 60, playerTextPos, playerColor, playerText.c_str());
			ImGui::GetBackgroundDrawList()->AddText(NULL, 60, ImVec2(botTextPos.x - 0.8f, botTextPos.y - 0.8f), outlineColor1, botText.c_str());
			ImGui::GetBackgroundDrawList()->AddText(NULL, 60, ImVec2(botTextPos.x + 0.8f, botTextPos.y + 0.8f), outlineColor1, botText.c_str());
			ImGui::GetBackgroundDrawList()->AddText(NULL, 60, botTextPos, botColor, botText.c_str());
		}
		else if (!IsGameStart)
		{
			string Text = "";
			Text += "等待进入游戏对局";
			ImVec2 TextSize = ImGui::CalcTextSize(Text.c_str());
			drawlist->AddText(NULL, 42, {resolution_information.Width - (TextSize.x / 2), 40 - (TextSize.y / 2)}, ImColor(255, 0, 0, 255), Text.c_str());
		}
	}
}