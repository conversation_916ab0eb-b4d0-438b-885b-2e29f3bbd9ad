

#ifndef ReadData_h
#define ReadData_h

#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>

typedef void (^returndata)(NSArray *data);
@interface ReadData : NSObject

@property (nonatomic,  strong) UIBezierPath *YellowLayer;
@property (nonatomic,  strong) UIBezierPath *GreenLayer;
@property (nonatomic,  strong) UIBezierPath *WhiteLayer;
@property (nonatomic,  strong) UIBezierPath *RedLayer;

+ (instancetype)share;
- (void)ReturnData:(returndata)block;

@end

#endif /* ReadData_h */
