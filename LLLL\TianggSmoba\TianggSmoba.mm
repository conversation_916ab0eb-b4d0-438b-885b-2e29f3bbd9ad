//
//  TianggSmoba.m
//  TianggSmoba
//
//  Created by 梦三大牛 on 2023/9/11.
//

#import <Foundation/Foundation.h>
#import "TianggSmoba.h"
#import "MsdnMetalDraw.h"
//#import "MsUIWindow.h"
Vector2 MiniMapSize = {35,135};//小地图大小
static uintptr_t Moudule_Base;
static Matrix ViewMatrix;
int MyTeam = 1;
typedef  struct _HeroImg{
    ImTextureID tex;
    int32_t HeroID;
}HeroImg;
static std::vector<HeroImg>HeroImgList;
static UIImage *circleImage(UIImage*img,float r){
    UIGraphicsBeginImageContext(img.size);
    CGContextRef context = UIGraphicsGetCurrentContext();
    CGRect rect = CGRectMake(0, 0, r, r);
    CGContextSetLineWidth(context, 6);
    CGContextAddEllipseInRect(context, rect);
    CGContextClip(context);
    [img drawInRect:rect];
    UIImage *newImg = UIGraphicsGetImageFromCurrentImageContext();
    UIGraphicsEndImageContext();
    return newImg;
}

static unsigned char * loadImage(UIImage *image,size_t &width,size_t &height){
    CGImageRef spriteImage = image.CGImage;
    width = CGImageGetWidth(spriteImage);
    height = CGImageGetHeight(spriteImage);
    unsigned char * spriteData = (unsigned char*)malloc(width*height*4);
    CGContextRef spriteContext = CGBitmapContextCreate(spriteData, width, height, 8, width*4,
                                                       CGImageGetColorSpace(spriteImage), kCGImageAlphaPremultipliedLast);
    CGContextDrawImage(spriteContext, CGRectMake(0, 0, width, height), spriteImage);
    CGContextRelease(spriteContext);
    return spriteData;
}
static ImTextureID GetHeroImageByID(int32_t ID,NSURL *url){
    if (!ID) return NULL;
    for (HeroImg img : HeroImgList) {
        if(img.HeroID == ID)return img.tex;
    }
    HeroImg newImg;
    newImg.HeroID = ID;
    UIImage *image = [UIImage imageWithData:[[NSData alloc] initWithContentsOfURL:url]];
    if(image){
        UIImage *cir = circleImage(image, image.size.width > image.size.height ? image.size.height : image.size.width);
        image = cir;
        size_t pixelWidth,pixelHeight;
        unsigned char *imgData = loadImage(image,pixelWidth,pixelHeight);
        MTLTextureDescriptor *textureDescriptor = [MTLTextureDescriptor texture2DDescriptorWithPixelFormat:MTLPixelFormatRGBA8Unorm width:pixelWidth height:pixelHeight mipmapped:NO];
        
        textureDescriptor.usage = MTLTextureUsageShaderRead;
        textureDescriptor.pixelFormat = MTLPixelFormatRGBA8Unorm;
        texture = [device newTextureWithDescriptor:textureDescriptor];
        if(imgData){
            [texture replaceRegion:MTLRegionMake2D(0, 0, pixelWidth, pixelHeight) mipmapLevel:0 withBytes:imgData bytesPerRow:pixelWidth * 4];
            free(imgData);
            imgData = NULL;
        }
        newImg.tex = (void*)CFBridgingRetain(texture);
        HeroImgList.push_back(newImg);
        return (void*)CFBridgingRetain(texture);
    }
    newImg.tex = (void*)CFBridgingRetain(texture);
    HeroImgList.push_back(newImg);
    return NULL;
}

static Vector2 WorldToScreen(Vector2 ActorPos) {
    Vector2 Screen = {0,0};
    float ViewW;
    ViewW = ViewMatrix._13 * ActorPos.x + ViewMatrix._33 * ActorPos.y + ViewMatrix._43;
    if (ViewW < 0.01) return Screen;
    ViewW = 1/ViewW;
    Screen.x = (1+(ViewMatrix._11 * ActorPos.x + ViewMatrix._31 * ActorPos.y + ViewMatrix._41) * ViewW)*MsKuan/2;
    Screen.y = (1-(ViewMatrix._12 * ActorPos.x + ViewMatrix._32 * ActorPos.y + ViewMatrix._42) * ViewW)*MsGao/2;
    return Screen;
}
////小地图方框调整坐标
static Vector2 GetMiniMapScreen(Vector2 MiniMap,Vector2 HeroPos){
    Vector2 Pos;
    float transformation = ViewMatrix._11>0?1:-1;
    Pos.x = (50 + HeroPos.x*transformation)/100;
    Pos.y = (50 - HeroPos.y*transformation)/100;
    return {MiniMap.x + Pos.x*MiniMap.y,Pos.y*MiniMap.y};
}

static void GetRefreshMatrix(){
    long a0 = MsDuQu<long long>(Moudule_Base + 0x0C9D3550);
    if(!isVaildPtr(a0))return;
    long a1 = MsDuQu<long long>(a0 + 0xA0);
    if(!isVaildPtr(a1))return;
    long a2 = MsDuQu<long long>(MsDuQu<long long>(a1) + 0x10);
    if(!isVaildPtr(a2))return;
    long a3 = MsDuQu<long long>(a2 + 0x30);
    if(!isVaildPtr(a3))return;
    long a4 = MsDuQu<long long>(a3 + 0x30);
    if(!isVaildPtr(a4))return;
    long a5 = MsDuQu<long long>(a4 + 0x18) + 0x2C8;
    if(!isVaildPtr(a5))return;
    Read(a5,&ViewMatrix,64);
}
static Vector2 GetActorPosition(int64_t a1){
    Vector2 pos;
    int64_t v1 = MsDuQu<long long>(a1 + 0x1F0);
    if(!isVaildPtr(v1))return pos;
    int64_t v2 = MsDuQu<long long>(v1 + 0x10);
    if(!isVaildPtr(v2))return pos;
    int64_t v3 = MsDuQu<long long>(v2 + 0x0);
    if(!isVaildPtr(v3))return pos;
    int64_t v4 = MsDuQu<long long>(v3 + 0x10);
    if(!isVaildPtr(v4))return pos;
    
    pos.x = (float)Read_Int(v4)/1000.0f;
    pos.y = (float)(Read_Int(v4 + 0x8))/1000.0f;
    return pos;
}
static int32_t GetHeroHealth(int64_t a1){
    int64_t healthLinker = MsDuQu<long long>(a1 + 0x148);
    if(!isVaildPtr(healthLinker))return 0;
    int32_t _HP = Read_Int(healthLinker + 0xA0);
    return _HP;
}
static int32_t GetHeroMaxHealth(int64_t a1){
    int64_t healthLinker = MsDuQu<long long>(a1 + 0x148);
    if(!isVaildPtr(healthLinker))return 0;
    int32_t Max = Read_Int(healthLinker + 0xA8);
    return Max;
}

static void MsdnSmobaHeroSkillFunction(long Hero,int32_t HeroID,Vector2 loc,ImDrawList *MsDrawList){
    int32_t HeroSkillTime1,HeroSkillTime2,HeroSkillTime3;
    int a1 = [[NSString stringWithFormat:@"%d10",HeroID] intValue];
    int a2 = [[NSString stringWithFormat:@"%d20",HeroID] intValue];
    int a3 = [[NSString stringWithFormat:@"%d30",HeroID] intValue];
    //===============英雄技能CD================
    // 120第四个技能 138不知道啥技能  168回城 回城  + 0xF8) + 0x168) + 0x110) + 0x20
    long HeroSkillList = MsDuQu<long>(Hero + 0x130);
    if(!isVaildPtr(HeroSkillList))return;
    long HeroSkill1 = MsDuQu<long>(HeroSkillList+0xD8);
    if(!isVaildPtr(HeroSkill1))return;
    long HeroSkill2 = MsDuQu<long>(HeroSkillList+0xF0);
    if(!isVaildPtr(HeroSkill2))return;
    long HeroSkill3 = MsDuQu<long>(HeroSkillList+0x108);
    if(!isVaildPtr(HeroSkill3))return;
    
    long HeroSkillTimeData1 = MsDuQu<long>(HeroSkill1+ 0xA0);
    if(!isVaildPtr(HeroSkillTimeData1))return;
    HeroSkillTime1 = Read_Int(HeroSkillTimeData1 + 0x38)/8192000;
    long HeroSkillTimeData2 = MsDuQu<long>(HeroSkill2+ 0xA0);
    if(!isVaildPtr(HeroSkillTimeData2))return;
    HeroSkillTime2 = Read_Int(HeroSkillTimeData2 + 0x38)/8192000;
    long HeroSkillTimeData3 = MsDuQu<long>(HeroSkill3+ 0xA0);
    if(!isVaildPtr(HeroSkillTimeData3))return;
    HeroSkillTime3 = Read_Int(HeroSkillTimeData3 + 0x38)/8192000;
    
    int32_t HeroSkillIDArray[1] = {a3};
    int32_t HeroSkillCDTime[1] = {HeroSkillTime3};
    int HeroSkillLoc = -120;
    for (int i = 0; i < 1; i++) {
        HeroSkillLoc += 40;
        int32_t ID = HeroSkillIDArray[i];
        int32_t CDTime = HeroSkillCDTime[i];
        ImTextureID HeroSkillIconImage = GetHeroImageByID(ID,[NSURL URLWithString:[NSString stringWithFormat:@"https://game.gtimg.cn/images/yxzj/img201606/heroimg/%d/%d.png",HeroID,ID]]);//加载技能图标
        if (!HeroSkillIconImage)continue;
        MsDrawList->AddImage(HeroSkillIconImage, ImVec2(loc.x+HeroSkillLoc+118+6.1, loc.y-78.9+40.3), ImVec2(loc.x+HeroSkillLoc+30+118+6.1, loc.y-48.9+40.3));
        
        if(!CDTime)continue;
        //        MsDrawList->AddCircleFilled(ImVec2(loc.x+HeroSkillLoc+15, loc.y+45), 15, 0x80808080);
        const char *Skill1Text = [[NSString stringWithFormat:@"%d",CDTime+1] UTF8String];
        MsDrawText((char*)Skill1Text, ImVec2(loc.x+HeroSkillLoc+15+118+6.1, loc.y-78.9+40.3), true, 0xFFFFFFFF, true,30);
        if(HeroSkillLoc >= 0)HeroSkillLoc = -120;
    }
    
    long HeroSkill4 = MsDuQu<long>(HeroSkillList+ 0x150);
    if(!isVaildPtr(HeroSkill4))return;
    int HeroSkillID4 = Read_Int(HeroSkill4 + 0x330);
    if (HeroSkillID4 > 80100||HeroSkill4 < 80200) {
        long HeroSkillTimeData4 = MsDuQu<long>(HeroSkill4+ 0xA0);
        int32_t HeroSkillTime4 = Read_Int(HeroSkillTimeData4 + 0x38)/8192000;
        ImTextureID HeroSkillIconImage = GetHeroImageByID(HeroSkillID4,[NSURL URLWithString:[NSString stringWithFormat:@"https://game.gtimg.cn/images/yxzj/img201606/summoner/%d.jpg",HeroSkillID4]]);//加载技能图标
        MsDrawList->AddImage(HeroSkillIconImage, ImVec2(loc.x+40+4.1, loc.y-141.8+69.4), ImVec2(loc.x+70+4.1, loc.y-111.8+69.4));
        
        int TextSize = 30;
        if (HeroSkillTime4){
            if (HeroSkillTime4 > 10) {
                TextSize = 20;
            }else if (HeroSkillTime4 < 10 && HeroSkillTime4 > 0){
                TextSize = 30;
            }else {
                TextSize = 30;
            }
            //            MsDrawList->AddCircleFilled(ImVec2(loc.x+55, loc.y+45), 15, 0x80808080);
            const char *Skill1Text = [[NSString stringWithFormat:@"%d",HeroSkillTime4] UTF8String];
            MsDrawText((char*)Skill1Text, ImVec2(loc.x+55+4.1, loc.y-141.8+69.4), true, 0xFFFFFFFF, true,TextSize);
        }
    }
}

static void TanggSmobaHeroDrawFunction(int64_t MsHeroData,int MsHeroCount,ImDrawList *MsDrawList){
    for (int i = 0; i < MsHeroCount; i++) {
        int64_t MsdnSmobaHero = MsDuQu<long long>(MsHeroData + 0x18*i);
        if(!isVaildPtr(MsdnSmobaHero))continue;;
        if(Read_Int(MsdnSmobaHero+0x34) == MyTeam)continue;
        int32_t HeroHP = GetHeroHealth(MsdnSmobaHero);
        if(HeroHP <= 0)continue;
        int32_t HeroMaxHP = GetHeroMaxHealth(MsdnSmobaHero);
        int32_t HeroID = Read_Int(MsdnSmobaHero + 0x28);
        if(HeroID == 620||HeroID == 621)continue;
        Vector2 HeroLocation = GetActorPosition(MsdnSmobaHero);
        NSLog(@"HOOK HeroLocation:X:%f HeroLocation:Y:%f ID:%d",HeroLocation.x,HeroLocation.y,HeroID);
        Vector2 MsdnMiNiMapLoc = GetMiniMapScreen(MiniMapSize, HeroLocation);
//        NSLog(@"HOOK X:%f Y:%f ID:%d",MsdnMiNiMapLoc.x,MsdnMiNiMapLoc.y,HeroID);
        
        Vector2 MapScreen = WorldToScreen(HeroLocation);
        
        ImTextureID HeroImage = GetHeroImageByID(HeroID,[NSURL URLWithString:[NSString stringWithFormat:@"https://game.gtimg.cn/images/yxzj/img201606/heroimg/%d/%d.jpg",HeroID,HeroID]]);//加载头像
        if(!HeroImage)continue;
        //==============小地图英雄=================
        //小地图英雄头像
        MsDrawList->AddImage(HeroImage, ImVec2(MsdnMiNiMapLoc.x-10, MsdnMiNiMapLoc.y-10), ImVec2(MsdnMiNiMapLoc.x+10, MsdnMiNiMapLoc.y+10));
        
        //小地图英雄血量背景
        MsDrawList->AddNgon(ImVec2(MsdnMiNiMapLoc.x, MsdnMiNiMapLoc.y), 10, 0xFFFFFFFF,50,3,M_PI_2 + M_PI,M_PI_2 - M_PI);
        //小地图英雄血量
        MsDrawList->AddNgon(ImVec2(MsdnMiNiMapLoc.x, MsdnMiNiMapLoc.y), 10, 0xFF507FFF,50,3,M_PI_2 + M_PI*HeroHP/HeroMaxHP,M_PI_2 - M_PI *HeroHP/HeroMaxHP);
        
        //==============大地图英雄=================
        MsDrawList->AddRect(ImVec2(MapScreen.x-35, MapScreen.y-90), ImVec2(MapScreen.x+35, MapScreen.y+10),ImColor(ImVec4(1, 1, 1, 0.6)));//方框
        
//        MsdnSmobaHeroSkillFunction(MsdnSmobaHero,HeroID,MapScreen,MsDrawList);//技能冷却计时及图标
        
    }
}
static void TanggSoldiersFunction(int64_t MsGame,ImDrawList *MsDrawList){
    int Soldiers_Count = Read_Int(MsGame + 0x124);//兵线
    if(Soldiers_Count <= 0)return;
    int64_t Soldiers_Data = MsDuQu<long long>(MsGame + 0x108);
    if(!isVaildPtr(Soldiers_Data))return;
    if(Soldiers_Data){
        for (int i = 0; i < Soldiers_Count; i++) {
            int64_t Soldiers = MsDuQu<long long>(Soldiers_Data + 0x18*i);
            if(!isVaildPtr(Soldiers))continue;;
            if(Read_Int(Soldiers+0x34) == MyTeam)continue;
            int32_t SoldiersHP = GetHeroHealth(Soldiers);
            if(SoldiersHP <= 0)continue;
            Vector2 SoldiersLocation = GetActorPosition(Soldiers);
            //==================小地图绘制====================
            Vector2 MiniSoldiersLoc = GetMiniMapScreen(MiniMapSize, SoldiersLocation);
            MsDrawList->AddCircleFilled(ImVec2(MiniSoldiersLoc.x, MiniSoldiersLoc.y), 1, 0xFFFACE87);
            //==================大地图绘制====================
            Vector2 MapScreen = WorldToScreen(SoldiersLocation);//转换屏幕坐标
            MsDrawList->AddCircleFilled(ImVec2(MapScreen.x, MapScreen.y), 5, ImColor(239, 68, 67));
        }
    }
}
static void TanggMonsterFunction(int64_t MsGame,ImDrawList *MsDrawList){
    int Monster_Count = Read_Int(MsGame + 0x164);
    if(Monster_Count <= 0)return;
    long Monster_Data = MsDuQu<long>(MsGame + 0x148);
    if(!isVaildPtr(Monster_Data))return;
    for (int i = 0; i < Monster_Count; i++) {
        long Monster = MsDuQu<long>(Monster_Data + 0x18*i);
        if(!isVaildPtr(Monster))continue;
        int32_t MonsterHP = GetHeroHealth(Monster);
        if(MonsterHP <= 0)continue;
        Vector2 MonsterLocation = GetActorPosition(Monster);
        int32_t MonsterMaxHP = GetHeroMaxHealth((Monster));
        int MonsterID = Read_Int(Monster + 0x28);
        if(MonsterID == 19610||MonsterID == 50110||MonsterID == 50400||MonsterID == 53300||MonsterID == 52700||MonsterID == 50004||MonsterID==6050||MonsterID==60131||MonsterID==6013||MonsterID==6008||MonsterID==60081||MonsterID==60521||MonsterID==6052||MonsterID==6054||MonsterID==6051||MonsterID==60132||MonsterID==60133||MonsterID==60531||MonsterID==6053||MonsterID==60541)continue;
        //==================小地图绘制野怪====================
        Vector2 MiniMonsterLoc = GetMiniMapScreen(MiniMapSize, MonsterLocation);
        MsDrawList->AddCircleFilled(ImVec2(MiniMonsterLoc.x, MiniMonsterLoc.y), 4, ImColor(243, 169, 61));
        
        MsDrawList->AddNgon(ImVec2(MiniMonsterLoc.x, MiniMonsterLoc.y), 4, 0xFF507FFF,50,2,M_PI_2 + M_PI,M_PI_2 - M_PI);//小地图血量
        MsDrawList->AddNgon(ImVec2(MiniMonsterLoc.x, MiniMonsterLoc.y), 4, 0xFFFFFF00,50,2,M_PI_2 + M_PI*MonsterHP/MonsterMaxHP,M_PI_2 - M_PI *MonsterHP/MonsterMaxHP);
        
        //==================大地图绘制====================
        Vector2 MapMonsterScreen = WorldToScreen(MonsterLocation);//转换屏幕坐标
        //方框
        MsDrawList->AddRect(ImVec2(MapMonsterScreen.x-35, MapMonsterScreen.y-90), ImVec2(MapMonsterScreen.x+35, MapMonsterScreen.y+10),0xFFB9DAFF,15.0f);
        
        //血量
        MsDrawList->AddRectFilled(ImVec2(MapMonsterScreen.x - 59+18.2, MapMonsterScreen.y + 9.5), ImVec2(MapMonsterScreen.x - 55+18.2, MapMonsterScreen.y + 29.5 - (float)MonsterHP/(float)MonsterMaxHP*120), 0xFF00FF00);
        //血量背景
        MsDrawList->AddRect(ImVec2(MapMonsterScreen.x - 60+18.2, MapMonsterScreen.y - 91.6), ImVec2(MapMonsterScreen.x-55+18.2, MapMonsterScreen.y+10.7), 0xFFFFFFFF);
    }
}
static void TanggMonsterDeathFunction(int64_t MsDead,ImDrawList *MsDrawList){
    int64_t MsMonsterDataV1 = MsDuQu<long long>(MsDead + 0x88);
    if(!isVaildPtr(MsMonsterDataV1))return;
    int64_t MsMonsterDataV3 = MsDuQu<long long>(MsMonsterDataV1 + 0x120);
    if(!isVaildPtr(MsMonsterDataV3))return;
    int MonsterDeathArr[16] = {0,24,264,408,432,456,360,48,72,384,288,312,336,240,192,216};
    for (int i = 0; i < 16; i++) {
        int64_t DeathMonster = MsDuQu<long long>(MsMonsterDataV3 + MonsterDeathArr[i]);
        int32_t MonsterTime = Read_Int(DeathMonster + 0x238)/1000 +3;
        if (!MonsterTime)continue;
        int32_t MonsterTimeMax = Read_Int(DeathMonster + 0x1E4)/1000 +3;
        if (!MonsterTimeMax)continue;
        Vector2 MonsterLoc = MsMonsterLocFun(MonsterDeathArr[i]);
        if (!MonsterLoc.x&&!MonsterLoc.y)continue;
        if(MonsterTime != MonsterTimeMax){
            Vector2 MiniMonsterLoc = GetMiniMapScreen(MiniMapSize, MonsterLoc);
            const char *MsTextHP = [[NSString stringWithFormat:@"%d",MonsterTime] UTF8String];
            MsDrawText((char*)MsTextHP, ImVec2(MiniMonsterLoc.x, MiniMonsterLoc.y-5), true, 0xFFFFFFFF, true, 15);
        }
    }
}
extern void TianggSmobaReadFunction(){
    GetRefreshMatrix();
    MyTeam = ViewMatrix._11>0?1:2;
    int64_t World = MsDuQu<long long>(Moudule_Base + 0x0C8175C0);
    if(!isVaildPtr(World))return;
    int64_t Game = MsDuQu<long long>(World + 0x378);
    if(!isVaildPtr(Game))return;
    int64_t Dead = MsDuQu<long long>(World + 0x3A8);
    if(!isVaildPtr(Dead))return;
    unsigned int Hero_count = Read_Int(Game + 0x7C);
    if (Hero_count <= 0)return;
    int64_t Hero_data = MsDuQu<long long>(Game + 0x60);
    if(!isVaildPtr(Hero_data))return;
    int GameStart = Read_Int(Hero_data + 0x108);
    if (GameStart != 0)return;
    ImDrawList *MsDrawList = ImGui::GetForegroundDrawList();
    MsDrawList->AddRect(ImVec2(MiniMapSize.x, 0), ImVec2(MiniMapSize.x+MiniMapSize.y, MiniMapSize.y),0xFFCBC0FF);//小地图调整方框
    
    TanggSmobaHeroDrawFunction(Hero_data,Hero_count,MsDrawList);//英雄
//    TanggSoldiersFunction(Game,MsDrawList);//兵线
    TanggMonsterFunction(Game,MsDrawList);//野怪
    TanggMonsterDeathFunction(Dead,MsDrawList);//野怪刷新计时
    
}

// 启动王者绘制
//static void __attribute__((constructor)) TianggAitoLoadFunction(){
//    NSLog(@"HOOK ===================");
//    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(10 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
//        MiniMapSize = {35,135};
//        NSTimer *MsShuaXin = [NSTimer scheduledTimerWithTimeInterval:5 repeats:YES block:^(NSTimer * _Nonnull timer) {
//            dispatch_sync(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
//                if(MsGetAppStartStatus((char*)"王者荣耀")){
//                    if(Moudule_Base <= 0)Moudule_Base = (long)MsdnGetGameModule_Base((char*)"smoba");
//                }else if(Moudule_Base > 0)Moudule_Base = 0;
//            });
//        }];
//        [[NSRunLoop currentRunLoop] addTimer:MsShuaXin forMode:NSRunLoopCommonModes];
//    });
//}
