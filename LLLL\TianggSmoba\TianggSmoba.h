//
//  TianggSmoba.h
//  TianggSmoba
//
//  Created by 梦三大牛 on 2023/9/11.
//

#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>
#import <objc/runtime.h>
#include <dlfcn.h>
#include <mach-o/dyld.h>
#include <mach/mach.h>
#include <sys/mman.h>
#include <mach-o/dyld_images.h>
#include <sys/sysctl.h>
#include <vector>
#import <spawn.h>
#import <string>
//#import "MsHomeMenuController.h"

typedef struct _Vector2{
    float x;
    float y;
}Vector2;
extern void TianggSmobaReadFunction();
extern Vector2 MiniMapSize;//小地图大小
//========================游戏常用固定数据========================
#define __int64 int64_t
class Vector3
{
public:
    float x,y,z;
    Vector3(){}
    Vector3(float nx, float ny, float nz) :x(nx), y(ny), z(nz){}
    
    void operator= (float a){
        x = a;
        y = a;
        z = a;
        
    }
    void operator= (Vector3 v){
        x = v.x;
        y = v.y;
        z = v.z;
    }
    Vector3 operator-(const Vector3 &a)const
    {
        return Vector3(x - a.x, y - a.y, z - a.z);
    }
    Vector3 operator/(float a) const
    {
        float temp = 1.0f / a;
        return Vector3(x*temp, y*temp, z*temp);
    }
    Vector3 operator*(float a /*标量*/) const
    {
        return Vector3(x*a, y*a, z*a);
    }
  Vector3 operator^(const Vector3& V) const
{
    return Vector3
        (
        y * V.z - z * V.y,
        z * V.x - x * V.z,
        x * V.y - y * V.x
        );
}
    Vector3 operator+(const Vector3 &a)const
    {
        return Vector3(x+a.x,y+a.y,z+a.z);
    }
    static float Dot(const Vector3 &a,const Vector3&b){
        return b.x*a.x + b.y*a.y + b.z*a.z;
    }
    
    static Vector3 Vector3ToRotation(Vector3 v1) {
        Vector3 V = Vector3(0,0,0);
        V.y = atan2(v1.y,v1.x);
        V.x = atan2(v1.z,sqrt(v1.x*v1.x+v1.y*v1.y));
        V.z = 0;
        return V;
    }
    static Vector3 FRotationToVector3(Vector3 v1){
        float CP, SP, CY, SY;
        CP = cos(v1.x);
        SP = sin(v1.x);
        CY = cos(v1.y);
        SY = sin(v1.y);
        return Vector3(CP*CY,CP*SY,SP);
    }
    static float CDistance(Vector3 v1,Vector3 v2){
        return sqrt(pow(v1.x-v2.x,2)+pow(v1.y-v2.y,2)+pow(v1.z-v2.z,2));
    }
    static Vector3 CMiddlePoint(Vector3 v1,Vector3 v2){
        Vector3 ret = Vector3(0,0,0);
        ret.x = (v1.x+v2.x)/2;
        ret.y = (v1.y+v2.y)/2;
        ret.z = (v1.z+v2.z)/2;
        return ret;
    }
    Vector3  operator*(const Vector3 &a)const
    {
        return Vector3(x*a.x,y*a.y,z*a.z);
    }
    Vector3 operator+(const float &a)const
    {
        return Vector3(x+a,y+a,z+a);
    }
    Vector3 operator-(const float &a)const
    {
        return Vector3(x-a,y-a,z-a);
    }
    Vector3 operator-()const{

        return Vector3(-x,-y,-z);
        
    }
    inline Vector3 RotateAngleAxis(const float AngleDeg, const Vector3& Axis) const{
        float S, C;
        S = sin(3.14159265f/180*AngleDeg);
        C = cos(3.14159265f/180*AngleDeg);
        
        const float XX    = Axis.x * Axis.x;
        const float YY    = Axis.y * Axis.y;
        const float ZZ    = Axis.z * Axis.z;
        
        const float XY    = Axis.x * Axis.y;
        const float YZ    = Axis.y * Axis.z;
        const float ZX    = Axis.z * Axis.x;
        
        const float XS    = Axis.x * S;
        const float YS    = Axis.y * S;
        const float ZS    = Axis.z * S;
        
        const float OMC    = 1.f - C;
        
        return Vector3(
                        (OMC * XX + C) * x + (OMC * XY - ZS) * y + (OMC * ZX + YS) * z,
                        (OMC * XY + ZS) * x + (OMC * YY + C) * y + (OMC * YZ - XS) * z,
                        (OMC * ZX - YS) * x + (OMC * YZ + XS) * y + (OMC * ZZ + C) * z
                        );
    }
    
};

struct Matrix{
    float _11;
    float _12;
    float _13;
    float _14;
    float _21;
    float _22;
    float _23;
    float _24;
    float _31;
    float _32;
    float _33;
    float _34;
    float _41;
    float _42;
    float _43;
    float _44;
};
static bool isVaildPtr(long ptr){
    if(!ptr || (ptr < 0x5000000000 && ptr > 0x6000000000)) return false;
    else return true;
}
//=========================================跨进程读取=========================================
static mach_port_t get_task;
static pid_t Processpid;
extern "C" kern_return_t mach_vm_region_recurse(
                                                vm_map_t                 map,
                                                mach_vm_address_t        *address,
                                                mach_vm_size_t           *size,
                                                uint32_t                 *depth,
                                                vm_region_recurse_info_t info,
                                                mach_msg_type_number_t   *infoCnt);

static pid_t MsdnGetGameProcesspid(char* GameProcessName) {
    size_t length = 0;
    static const int name[] = {CTL_KERN, KERN_PROC, KERN_PROC_ALL, 0};
    int err = sysctl((int *)name, (sizeof(name) / sizeof(*name)) - 1, NULL, &length, NULL, 0);
    if (err == -1) err = errno;
    if (err == 0) {
        struct kinfo_proc *procBuffer = (struct kinfo_proc *)malloc(length);
        if(procBuffer == NULL) return -1;
        sysctl((int *)name, (sizeof(name) / sizeof(*name)) - 1, procBuffer, &length, NULL, 0);
        int count = (int)length / sizeof(struct kinfo_proc);
        for (int i = 0; i < count; ++i) {
            const char *procname = procBuffer[i].kp_proc.p_comm;
            Processpid = procBuffer[i].kp_proc.p_pid;
            if(strstr(procname,GameProcessName)){
                return Processpid;
            }
        }
    }
    return  -1;
}

static vm_map_offset_t MsdnGetGameModule_Base(char* GameProcessName) {
    vm_map_offset_t vmoffset = 0;
    vm_map_size_t vmsize = 0;
    uint32_t nesting_depth = 0;
    struct vm_region_submap_info_64 vbr;
    mach_msg_type_number_t vbrcount = 16;
    pid_t pid = MsdnGetGameProcesspid(GameProcessName);
    kern_return_t kret = task_for_pid(mach_task_self(), pid, &get_task);
    if (kret == KERN_SUCCESS) {
        mach_vm_region_recurse(get_task, &vmoffset, &vmsize, &nesting_depth, (vm_region_recurse_info_t)&vbr, &vbrcount);
    }
    return vmoffset;
}

static bool Read(long ptr, void *buffer, int length){
    if(ptr <= 0 || ptr > 100000000000 || isnan(ptr))return false;
    vm_size_t size = 0;
    kern_return_t error = vm_read_overwrite(get_task, (vm_address_t)ptr, length, (vm_address_t)buffer, &size);
    if(error != KERN_SUCCESS || size != length) {
        return false;
    }
    return true;
}
template<typename T> T MsDuQu(long address) {
    T data;
    Read(address, reinterpret_cast<void *>(&data), sizeof(T));
    return data;
}

static int Read_Int(long address){
    int result=0;
    Read(address,&result,2);
    return result;
}
static bool MsGetAppStartStatus(char *MsGameName){
    id FrontApp = [[NSClassFromString([NSString stringWithUTF8String:"SpringBoard"]) sharedApplication] performSelector:NSSelectorFromString([NSString stringWithUTF8String:"_accessibilityFrontMostApplication"])];
    NSString *displayName = [FrontApp performSelector:NSSelectorFromString([NSString stringWithUTF8String:"displayName"])];
    if([displayName isEqualToString:[NSString stringWithUTF8String:MsGameName]]){
        return YES;
    }
    return NO;
}

static Vector2 MsMonsterLocFun(int offset){
    Vector2 loc;//蓝方
    if(offset == 0){//蓝Buff
        loc.x = -23.14;
        loc.y = 1.3;
    }else if(offset == 24){//红Buff
        loc.x = 2.588;
        loc.y = -30;
    }else if(offset == 264){//蜥蜴
        loc.x = -36.16;
        loc.y = 4.495;
    }else if(offset == 288){//穿山甲
        loc.x = -33.252;
        loc.y = 20;
    }else if(offset == 312){//猪
        loc.x = -3.657;
        loc.y = -18.733;
    }else if(offset == 336){//鸟
        loc.x = 16.74;
        loc.y = -36.072;
    }else if(offset == 240){//狼
        loc.x = -30.266;
        loc.y = -9.662;
    }else if(offset == 48){//==红方===蓝BUFF
        loc.x = 23.151;
        loc.y = -0.846;
    }else if(offset == 72){//红BUFF
        loc.x = -2.427;
        loc.y = 29.948;
    }else if(offset == 384){//蜥蜴
        loc.x = 36.371;
        loc.y = -4.302;
    }else if(offset == 408){//穿山甲
        loc.x = 33.173;
        loc.y = -20.75;
    }else if(offset == 432){//猪
        loc.x = 3.655;;
        loc.y = 18.843;
    }else if(offset == 456){//鸟
        loc.x = -16.649;
        loc.y = 35.984;
    }else if(offset == 360){//狼
        loc.x = 30.266;
        loc.y = 9.662;
    }else if(offset == 192){//上路河道精灵
        loc.x = -34.09;
        loc.y = 34.09;
    }else if(offset == 216){//下路河道精灵
        loc.x = 35.5;
        loc.y = -35.5;
    }else if(offset == 536){//上路河道精灵
        loc.x = -34.09;
        loc.y = 34.09;
    }else if(offset == 664){//下路河道精灵
        loc.x = 35.5;
        loc.y = -35.5;
    }
    
    return loc;
    
    
}
